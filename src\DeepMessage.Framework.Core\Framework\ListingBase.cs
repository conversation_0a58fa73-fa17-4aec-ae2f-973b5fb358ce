﻿using DeepMessage.Framework.Enums;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Platform.Framework.Core;
using System.ComponentModel;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace DeepMessage.Framework.Core
{
    public abstract class ListingBase<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService> : FrameworkBaseComponent, INotifyPropertyChanged
    where TListViewModel : class
    where TListBusinessObject : class
    where TService : IListingDataService<TListBusinessObject, TFilterBusinessObject>
    where TFilterViewModel : BaseFilterViewModel, new()
    where TFilterBusinessObject : BaseFilterBusinessObject, new()

    {
        [CascadingParameter]
        public IEnumerable<SystemClaimType> UserClaims { get; set; }

        #region Index Properties

        public int TotalPages { get; set; }

        private string _sortColumn;

        public string SortColumn
        {
            get { return _sortColumn; }
            set
            {
                _sortColumn = value;
                NotifyPropertyChanged();
            }
        }

        [Inject]
        protected ILogger<TListViewModel> Logger { get; set; } = null!;

        //public string? ErrorMessage { get; set; }


        private bool _isTableBusy = true;
        public bool IsWorking
        {
            get
            {
                return _isTableBusy;
            }
            set
            {
                _isTableBusy = value;
                //if (Layout != null)
                //{
                //    Layout.IsTableBusy = value;
                //}
            }
        }
        private int _totalRows;
        public int TotalRecords
        {
            get
            {
                return _totalRows;
            }
            set
            {
                if (_totalRows != value)
                    _totalRows = value;
            }
        }
        public string? Error { get; set; }
        #endregion


        public TFilterViewModel FilterViewModel { get; set; } = new TFilterViewModel();
        public TService ServiceList { get; set; }

        private List<TListViewModel> _items = [];
        protected List<TListViewModel> Items
        {
            get
            {
                return _items;
            }
            set
            {

                _items = value;
                NotifyPropertyChanged();

            }
        }

        protected virtual PaginationStripModel PaginationStrip { get; set; }


        private int _rowsPerPage = 10;

        public virtual int PageSize
        {
            get { return _rowsPerPage; }
            set
            {
                if (_rowsPerPage != value)
                {
                    _rowsPerPage = value;
                    NotifyPropertyChanged();
                }
            }
        }


        public virtual bool UsePagination => true;

        public string _componentId = Guid.NewGuid().ToString().Replace("-", "");

        public class ItemsLoadEventArgs
        {
            public ItemsLoadEventArgs(IEnumerable<TListViewModel> items, bool isFirstTime)
            {
                Items = items;
                IsFirstTime = isFirstTime;
            }

            public IEnumerable<TListViewModel> Items { get; set; }
            public bool IsFirstTime { get; set; }
        }

        [Parameter]
        public EventCallback<ItemsLoadEventArgs> OnItemsRendered { get; set; }

        public ListingBase()
        {
            PaginationStrip = new PaginationStripModel()
            {
                CurrentIndex = 1,
                RowsPerPage = PageSize
            };
            this.PropertyChanged += ListingComponent_PropertyChanged;
        }


        protected override async Task OnInitializedAsync()
        {

            await OnFrameworkParametersSet();

            FilterViewModel.PropertyChanged += FilterViewModel_PropertyChanged;
            PaginationStrip.PropertyChanged += async (p, q) =>
            {
                if (q.PropertyName == nameof(PaginationStrip.RowsPerPage)
                || q.PropertyName == nameof(PaginationStrip.CurrentIndex)
                || q.PropertyName == nameof(SortColumn))
                {

                    if (PaginationStrip.CurrentIndex < 0)
                    {
                        PaginationStrip.CurrentIndex = 1;
                    }
                    else if (TotalPages > 0 && PaginationStrip.CurrentIndex > TotalPages)
                    {
                        PaginationStrip.CurrentIndex = TotalPages;
                    }

                    FilterViewModel.RowsPerPage = PaginationStrip.RowsPerPage;
                    FilterViewModel.CurrentIndex = (TotalRecords <= PaginationStrip.RowsPerPage) ? 1 : PaginationStrip.CurrentIndex;
                    await LoadItems();

                }
            };

            if (LoadItemsOnInit)
            {
                await LoadItems();

            }

            using var scope = ScopeFactory?.CreateScope();
            await LoadSelectLists(scope);
            await InvokeAsync(() => StateHasChanged());
            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                IsWorking = false;
                if (!LoadItemsOnInit) await LoadItems();
            }

            await base.OnAfterRenderAsync(firstRender);
        }

        //protected override void BuildRenderTree(RenderTreeBuilder builder)
        //{
        //    var componentName = GetType().Name;

        //    // Start comment
        //    builder.OpenElement(0, "!--");
        //    builder.AddContent(1, $" Start: {componentName} ");
        //    builder.CloseElement();

        //    // Render the component's actual content
        //    base.BuildRenderTree(builder);

        //    // End comment
        //    builder.OpenElement(2, "!--");
        //    builder.AddContent(3, $" End: {componentName} ");
        //    builder.CloseElement();
        //}
        public virtual void ListingComponent_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {

        }

        protected virtual void FilterViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            _ = LoadItems();
        }

        protected virtual Task LoadSelectLists(IServiceScope scope)
        {
            return Task.CompletedTask;
        }

        public async Task<T> InvokeAsync<TParam, T>(
                 Func<TParam, Task<T>> methodToInvoke,
                 TParam param, TService serci)
        {
            ValidatePermission(methodToInvoke.Method.Name, serci);
            return await methodToInvoke(param);
        }

        public void ValidatePermission(string methodName, TService service)
        {

            Type serviceType = service.GetType();
            var methodInfo = serviceType.GetMethod(methodName);
            if (methodInfo == null)
                throw new Exception("Method not found");

            var systemClaimAttributes = methodInfo.GetCustomAttributes<SystemClaimAttribute>(true);
            if (systemClaimAttributes == null)
                throw new Exception($"Permissions are not configured for {serviceType.Name}.{methodName}");

            var systemClaimTypes = systemClaimAttributes.Select(y => y.ClaimType).ToList();

            if (systemClaimTypes.Count > 0 && UserClaims != null && !systemClaimTypes.Intersect(UserClaims).Any())
                throw new Exception($"Not Allowed {serviceType.Name} - {methodName} - with claim {string.Join(',', systemClaimTypes)}");
        }


        protected virtual async Task ItemsLoaded(TService service)
        {
            await Task.CompletedTask;
        }

        protected virtual int GetCountQuery()
        {
            return -1;
        }

        protected async Task LoadItems()
        {
            await LoadItems(true);
        }

        protected virtual List<TListViewModel> ConvertToListViewModel(List<TListBusinessObject> list)
        {
            return list.Clone<List<TListViewModel>>();
        }
        bool firstTimeItemsLoadedFlag = true;
        virtual public bool LoadItemsOnInit { get; } = false;
        protected async Task LoadItems(bool showLoader)
        {

            if (showLoader)
            {
                IsWorking = true;
                await InvokeAsync(() => StateHasChanged());
                await Task.Delay(10);
            }

            using (var scope = ScopeFactory!.CreateScope())
            {
                try
                {
                    var crudService = scope.ServiceProvider.GetRequiredService<TService>();
                    var FilterBusinessObject = FilterViewModel.Clone<TFilterBusinessObject>();
                    //var pagedItems = await crudService.GetPaginatedItems(FilterBusinessObject);
                    var pagedItems = await InvokeAsync(crudService.GetPaginatedItems, FilterBusinessObject, crudService);
                    if (pagedItems == null)
                        throw new Exception($"Paginated items null from {typeof(TService)}");

                    Items = ConvertToListViewModel(pagedItems.Items);
                    TotalPages = pagedItems.TotalPages;
                    TotalRecords = pagedItems.TotalRows;
                    await ItemsLoaded(crudService);
                }
                catch (UnauthorizedAccessException)
                {
                    Navigation?.NavigateTo($"/home/<USER>");
                }
                catch (Exception ex)
                {
                    NotificationService.ShowError(ex);
                    Logger.LogError($"Failed LoadItems {ex}");
                }

                IsWorking = false;
                await InvokeAsync(() => StateHasChanged());
            }

            await OnItemsRendered.InvokeAsync(new ItemsLoadEventArgs(Items, firstTimeItemsLoadedFlag));
            firstTimeItemsLoadedFlag = false;
        }

        protected virtual Task OnFrameworkParametersSet()
        {


            return Task.CompletedTask;
        }

        public override void DialogService_OnClose(dynamic obj)
        {
            _ = LoadItems();
        }


        public event PropertyChangedEventHandler PropertyChanged;

        public void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public TService GetService()
        {
            var scope = ScopeFactory.CreateScope();
            return scope.ServiceProvider.GetRequiredService<TService>();
        }



    }
}