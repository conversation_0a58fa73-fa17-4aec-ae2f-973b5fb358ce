﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ProfileFormViewBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.ProfileFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="My Profile"
    x:DataType="local:ProfileFormView"
    BackgroundColor="#F9FAFB"
    Shell.BackgroundColor="#004f98"
    Shell.TitleColor="White">

    <Grid RowDefinitions="Auto,*">

        <!--  Header Section  -->
        <Border
            Grid.Row="0"
            BackgroundColor="White"
            StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="0,0,0,0" />
            </Border.StrokeShape>
            <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">

                <!--  Back Button  -->
                <Button
                    Grid.Column="0"
                    Padding="8"
                    BackgroundColor="Transparent"
                    Command="{Binding BackCommand}"
                    CornerRadius="20"
                    HeightRequest="40"
                    WidthRequest="40">
                    <Button.ImageSource>
                        <FileImageSource File="arrow_left_icon.svg" />
                    </Button.ImageSource>
                </Button>

                <!--  Title  -->
                <Label
                    Grid.Column="1"
                    FontAttributes="Bold"
                    FontFamily="MulishExtraBold"
                    FontSize="20"
                    HorizontalOptions="Center"
                    Text="My Profile"
                    TextColor="#111827"
                    VerticalOptions="Center" />

                <!--  Save Button  -->
                <Button
                    Grid.Column="2"
                    Padding="16,8"
                    BackgroundColor="#004f98"
                    Command="{Binding SaveCommand}"
                    CornerRadius="8"
                    FontAttributes="Bold"
                    FontSize="14"
                    IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}"
                    Text="{Binding IsWorking, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Saving...|Save'}"
                    TextColor="White" />
            </Grid>
        </Border>

        <!--  Content Section  -->
        <ScrollView Grid.Row="1">
            <VerticalStackLayout Spacing="0">

                <!--  Error Message  -->
                <Border
                    Margin="16,16,16,0"
                    BackgroundColor="#FEF2F2"
                    IsVisible="{Binding HasError}"
                    Stroke="#FECACA"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Image
                            Grid.Column="0"
                            Source="error_icon.svg"
                            WidthRequest="20"
                            HeightRequest="20"
                            Margin="0,2,8,0"
                            VerticalOptions="Start" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            Text="{Binding Error}"
                            TextColor="#DC2626" />
                    </Grid>
                </Border>

                <!--  Success Message  -->
                <Border
                    Margin="16,16,16,0"
                    BackgroundColor="#F0FDF4"
                    IsVisible="{Binding HasSuccessMessage}"
                    Stroke="#BBF7D0"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Image
                            Grid.Column="0"
                            Source="check_icon.svg"
                            WidthRequest="20"
                            HeightRequest="20"
                            Margin="0,2,8,0"
                            VerticalOptions="Start" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            Text="{Binding SuccessMessage}"
                            TextColor="#047857" />
                    </Grid>
                </Border>

                <!--  Profile Display Section  -->
                <Border
                    Margin="0,16,0,0"
                    BackgroundColor="White"
                    StrokeThickness="0">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="0" />
                    </Border.StrokeShape>
                    <Grid
                        Padding="16"
                        ColumnDefinitions="Auto,*"
                        RowDefinitions="Auto">

                        <!--  Avatar Display  -->
                        <Border
                            Grid.Column="0"
                            BackgroundColor="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Transparent|#E5E7EB'}"
                            HeightRequest="100"
                            Stroke="#004f98"
                            StrokeThickness="3"
                            VerticalOptions="Start"
                            WidthRequest="100">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="50" />
                            </Border.StrokeShape>
                            <Grid>
                                <Image
                                    Aspect="AspectFill"
                                    IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                    Source="{Binding SelectedItem.AvatarData}" />
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="32"
                                    HorizontalOptions="Center"
                                    IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                    Text="{Binding SelectedItem.DisplayName, Converter={StaticResource InitialsConverter}}"
                                    TextColor="#004f98"
                                    VerticalOptions="Center" />
                            </Grid>
                        </Border>

                        <!--  Name and Info  -->
                        <VerticalStackLayout
                            Grid.Column="1"
                            Margin="16,0,0,0"
                            Spacing="4"
                            VerticalOptions="Center">
                            <Label
                                FontAttributes="Bold"
                                FontSize="20"
                                LineBreakMode="TailTruncation"
                                Text="{Binding SelectedItem.DisplayName, TargetNullValue='No Name'}"
                                TextColor="#111827" />
                            <Label
                                FontSize="14"
                                Text="Profile Information"
                                TextColor="#6B7280" />
                        </VerticalStackLayout>
                    </Grid>
                </Border>

                <!--  Form Fields Section  -->
                <VerticalStackLayout
                    Margin="0,8,0,0"
                    Padding="16"
                    BackgroundColor="White"
                    Spacing="24">

                    <!--  Avatar Selection  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Select Avatar"
                            TextColor="#374151" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            Text="Choose from predefined avatar images"
                            TextColor="#6B7280" />

                        <!--  Avatar Preview Row  -->
                        <HorizontalStackLayout Margin="0,8" Spacing="12">
                            <Label
                                FontSize="14"
                                Text="Preview:"
                                TextColor="#6B7280"
                                VerticalOptions="Center" />
                            <Border
                                BackgroundColor="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Transparent|#E5E7EB'}"
                                HeightRequest="50"
                                Stroke="#004f98"
                                StrokeThickness="2"
                                WidthRequest="50">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="25" />
                                </Border.StrokeShape>
                                <Grid>
                                    <Image
                                        Aspect="AspectFill"
                                        IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                        Source="{Binding SelectedItem.AvatarData}" />
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="18"
                                        HorizontalOptions="Center"
                                        IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                        Text="{Binding SelectedItem.DisplayName, Converter={StaticResource InitialsConverter}}"
                                        TextColor="#004f98"
                                        VerticalOptions="Center" />
                                </Grid>
                            </Border>
                        </HorizontalStackLayout>

                        <Border
                            BackgroundColor="#F9FAFB"
                            HeightRequest="48"
                            Stroke="#E5E7EB"
                            StrokeThickness="1">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="8" />
                            </Border.StrokeShape>
                            <Picker
                                Title="Choose an avatar..."
                                Margin="12,0"
                                FontSize="14"
                                ItemsSource="{Binding AvatarOptions}"
                                SelectedItem="{Binding SelectedItem.AvatarData}"
                                TextColor="#374151"
                                TitleColor="#9CA3AF" />
                        </Border>
                    </VerticalStackLayout>

                    <!--  Display Name  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Display Name"
                            TextColor="#374151" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            LineBreakMode="WordWrap"
                            Text="This name will be visible to your friends when you add them as your friend"
                            TextColor="#6B7280" />
                        <Border
                            BackgroundColor="#F9FAFB"
                            HeightRequest="48"
                            Stroke="#E5E7EB"
                            StrokeThickness="1">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="8" />
                            </Border.StrokeShape>
                            <Entry
                                Margin="12,0"
                                BackgroundColor="Transparent"
                                FontSize="16"
                                Placeholder="Enter your display name"
                                PlaceholderColor="#9CA3AF"
                                Text="{Binding SelectedItem.DisplayName}"
                                TextColor="#374151" />
                        </Border>
                    </VerticalStackLayout>

                    <!--  Avatar Description (if needed for AI generation)  -->
                    <VerticalStackLayout IsVisible="False" Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Avatar Description"
                            TextColor="#374151" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            Text="Description used for AI avatar generation (optional)"
                            TextColor="#6B7280" />
                        <Border
                            BackgroundColor="#F9FAFB"
                            HeightRequest="80"
                            Stroke="#E5E7EB"
                            StrokeThickness="1">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="8" />
                            </Border.StrokeShape>
                            <Editor
                                Margin="12,8"
                                AutoSize="TextChanges"
                                BackgroundColor="Transparent"
                                FontSize="16"
                                MaxLength="450"
                                Placeholder="Describe the avatar you want to generate..."
                                PlaceholderColor="#9CA3AF"
                                Text="{Binding SelectedItem.AvatarDescription}"
                                TextColor="#374151" />
                        </Border>
                    </VerticalStackLayout>

                    <!--  Loading Indicator  -->
                    <ActivityIndicator
                        Margin="0,16"
                        IsRunning="{Binding IsWorking}"
                        IsVisible="{Binding IsWorking}"
                        Color="#004f98" />

                </VerticalStackLayout>

                <!--  Bottom Spacing  -->
                <BoxView HeightRequest="32" Color="Transparent" />
            </VerticalStackLayout>
        </ScrollView>
    </Grid>
</local:ProfileFormViewBase>
