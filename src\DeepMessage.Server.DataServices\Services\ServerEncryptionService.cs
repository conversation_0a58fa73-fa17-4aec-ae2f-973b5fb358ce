using System.Security.Cryptography;
using System.Text;

namespace DeepMessage.Server.DataServices.Services
{
    /// <summary>
    /// Server-side encryption service for RSA public key operations
    /// Used for encrypting messages with recipient public keys
    /// </summary>
    public interface IServerEncryptionService
    {
        /// <summary>
        /// Encrypts plaintext using RSA public key for end-to-end message encryption
        /// </summary>
        /// <param name="plaintext">The message content to encrypt</param>
        /// <param name="publicKeyPem">Base64-encoded RSA public key</param>
        /// <returns>Base64-encoded encrypted message</returns>
        string EncryptWithRSAPublicKey(string plaintext, string publicKeyPem);
    }

    public class ServerEncryptionService : IServerEncryptionService
    {
        /// <summary>
        /// Encrypts plaintext using RSA public key for end-to-end message encryption
        /// </summary>
        /// <param name="plaintext">The message content to encrypt</param>
        /// <param name="publicKeyPem">Base64-encoded RSA public key</param>
        /// <returns>Base64-encoded encrypted message</returns>
        public string EncryptWithRSAPublicKey(string plaintext, string publicKeyPem)
        {
            if (string.IsNullOrEmpty(plaintext))
                throw new ArgumentException("Plaintext cannot be null or empty.", nameof(plaintext));
            
            if (string.IsNullOrEmpty(publicKeyPem))
                throw new ArgumentException("Public key cannot be null or empty.", nameof(publicKeyPem));

            try
            {
                using var rsa = RSA.Create();
                var publicKeyBytes = Convert.FromBase64String(publicKeyPem);
                rsa.ImportRSAPublicKey(publicKeyBytes, out _);

                var plaintextBytes = Encoding.UTF8.GetBytes(plaintext);
                var encryptedBytes = rsa.Encrypt(plaintextBytes, RSAEncryptionPadding.OaepSHA256);
                
                return Convert.ToBase64String(encryptedBytes);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to encrypt message with RSA public key: {ex.Message}", ex);
            }
        }
    }
}
