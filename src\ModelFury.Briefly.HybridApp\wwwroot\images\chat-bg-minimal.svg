<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Ultra-Subtle Nothing Phone Gradients -->
    <linearGradient id="ultraSubtle" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#fafafa;stop-opacity:1"/>
    </linearGradient>
    
    <radialGradient id="whisperGradient" cx="50%" cy="50%" r="80%">
      <stop offset="0%" style="stop-color:#f5f5f5;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#eeeeee;stop-opacity:0.1"/>
    </radialGradient>
    
    <!-- Micro Pattern -->
    <pattern id="microDots" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
      <circle cx="40" cy="40" r="0.5" fill="#e0e0e0" opacity="0.15"/>
    </pattern>
  </defs>
  
  <!-- Pure Base -->
  <rect width="400" height="600" fill="url(#ultraSubtle)"/>
  
  <!-- Whisper-Soft Shapes -->
  <circle cx="150" cy="200" r="100" fill="url(#whisperGradient)" opacity="0.4"/>
  <circle cx="300" cy="450" r="80" fill="url(#whisperGradient)" opacity="0.3"/>
  
  <!-- Barely-There Lines -->
  <path d="M0,250 Q200,230 400,250" stroke="#eeeeee" stroke-width="0.5" fill="none" opacity="0.6"/>
  <path d="M0,400 Q150,380 400,400" stroke="#e0e0e0" stroke-width="0.3" fill="none" opacity="0.5"/>
  
  <!-- Micro Texture -->
  <rect width="400" height="600" fill="url(#microDots)" opacity="0.8"/>
  
  <!-- Nothing Phone Signature - Ultra Minimal -->
  <rect x="10" y="10" width="1" height="20" fill="#bdbdbd" opacity="0.3"/>
  <rect x="389" y="570" width="1" height="20" fill="#bdbdbd" opacity="0.3"/>
  
  <!-- Ghost Message Bubbles -->
  <ellipse cx="280" cy="180" rx="30" ry="15" fill="#f5f5f5" opacity="0.4"/>
  <ellipse cx="120" cy="380" rx="25" ry="12" fill="#eeeeee" opacity="0.3"/>
</svg>
