﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ProfileListingViewBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.ProfileListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="My Profile"
    x:DataType="local:ProfileListingView"
    BackgroundColor="#F9FAFB"
    Shell.BackgroundColor="#004f98"
    Shell.TitleColor="White">
    
    <Grid RowDefinitions="Auto,*">
        
        <!-- Header Section -->
        <Border Grid.Row="0" BackgroundColor="White" StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="0,0,0,0" />
            </Border.StrokeShape>
            <Grid Padding="16,16,16,12">
                <Label FontSize="28"
                       FontAttributes="Bold"
                       FontFamily="MulishExtraBold"
                       Text="My Profile"
                       TextColor="#004f98"
                       VerticalOptions="Center"
                       HorizontalOptions="Start" />
            </Grid>
        </Border>

        <!-- Content Section -->
        <ScrollView Grid.Row="1" IsVisible="{Binding HasProfileData}">
            <VerticalStackLayout Spacing="0">
                
                <!-- Profile Display Section -->
                <Border BackgroundColor="White" 
                        StrokeThickness="0"
                        Margin="0,0,0,8">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="0" />
                    </Border.StrokeShape>
                    <VerticalStackLayout Padding="16,24" Spacing="16">
                        
                        <!-- Avatar and Display Name -->
                        <VerticalStackLayout HorizontalOptions="Center" Spacing="12">
                            <!-- Avatar Display -->
                            <Border
                                BackgroundColor="{Binding Item.AvatarData, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Transparent|#E5E7EB'}"
                                WidthRequest="120"
                                HeightRequest="120"
                                StrokeThickness="4"
                                Stroke="#004f98"
                                HorizontalOptions="Center">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="60" />
                                </Border.StrokeShape>
                                <Image Source="{Binding Item.AvatarData}"
                                       IsVisible="{Binding Item.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                       Aspect="AspectFill" />
                                <Label IsVisible="{Binding Item.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                       Text="{Binding Item.DisplayName, Converter={StaticResource InitialsConverter}}"
                                       FontSize="36"
                                       FontAttributes="Bold"
                                       TextColor="#004f98"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center" />
                            </Border>
                            
                            <!-- Display Name and Description -->
                            <VerticalStackLayout HorizontalOptions="Center" Spacing="8">
                                <Label FontSize="24" 
                                       FontAttributes="Bold" 
                                       Text="{Binding Item.DisplayName, TargetNullValue='No Name'}" 
                                       TextColor="#111827"
                                       HorizontalTextAlignment="Center" />
                                <Label FontSize="14" 
                                       Text="This name will be visible to your friends when you add them as your friend, they can change your name in their contact list also" 
                                       TextColor="#6B7280"
                                       HorizontalTextAlignment="Center"
                                       LineBreakMode="WordWrap"
                                       MaxLines="3"
                                       Margin="8,0" />
                            </VerticalStackLayout>
                        </VerticalStackLayout>
                    </VerticalStackLayout>
                </Border>

                <!-- Navigation Links Section -->
                <VerticalStackLayout Spacing="8">
                    
                    <!-- Update Profile -->
                    <Border BackgroundColor="White" StrokeThickness="0">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="0" />
                        </Border.StrokeShape>
                        <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                            <Border Grid.Column="0"
                                    BackgroundColor="#EBF8FF"
                                    WidthRequest="40"
                                    HeightRequest="40"
                                    VerticalOptions="Center">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="20" />
                                </Border.StrokeShape>
                                <Path Fill="#1E40AF" 
                                      VerticalOptions="Center"
                                      HorizontalOptions="Center"
                                      Data="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </Border>
                            
                            <VerticalStackLayout Grid.Column="1" 
                                                 Margin="12,0"
                                                 VerticalOptions="Center"
                                                 Spacing="2">
                                <Label FontSize="16" 
                                       FontAttributes="Bold" 
                                       Text="Update Profile" 
                                       TextColor="#111827" />
                                <Label FontSize="14" 
                                       Text="Edit your profile information and avatar" 
                                       TextColor="#6B7280" />
                            </VerticalStackLayout>
                            
                            <Path Grid.Column="2" 
                                  Fill="#9CA3AF" 
                                  VerticalOptions="Center"
                                  Data="M9 5l7 7-7 7" />
                                  
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding UpdateProfileCommand}" />
                            </Grid.GestureRecognizers>
                        </Grid>
                    </Border>

                    <!-- Change Password -->
                    <Border BackgroundColor="White" StrokeThickness="0">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="0" />
                        </Border.StrokeShape>
                        <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                            <Border Grid.Column="0"
                                    BackgroundColor="#FEF2F2"
                                    WidthRequest="40"
                                    HeightRequest="40"
                                    VerticalOptions="Center">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="20" />
                                </Border.StrokeShape>
                                <Path Fill="#DC2626" 
                                      VerticalOptions="Center"
                                      HorizontalOptions="Center"
                                      Data="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6h-2.5a2.5 2.5 0 01-2.5-2.5 2.5 2.5 0 012.5-2.5H16a2 2 0 002-2m-7 4v4m0 0H9m2 0h2m-2-4a2 2 0 11-4 0 2 2 0 014 0z" />
                            </Border>
                            
                            <VerticalStackLayout Grid.Column="1" 
                                                 Margin="12,0"
                                                 VerticalOptions="Center"
                                                 Spacing="2">
                                <Label FontSize="16" 
                                       FontAttributes="Bold" 
                                       Text="Change Password" 
                                       TextColor="#111827" />
                                <Label FontSize="14" 
                                       Text="Update your account password" 
                                       TextColor="#6B7280" />
                            </VerticalStackLayout>
                            
                            <Path Grid.Column="2" 
                                  Fill="#9CA3AF" 
                                  VerticalOptions="Center"
                                  Data="M9 5l7 7-7 7" />
                                  
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ChangePasswordCommand}" />
                            </Grid.GestureRecognizers>
                        </Grid>
                    </Border>

                    <!-- Referral Codes -->
                    <Border BackgroundColor="White" StrokeThickness="0">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="0" />
                        </Border.StrokeShape>
                        <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                            <Border Grid.Column="0"
                                    BackgroundColor="#F0FDF4"
                                    WidthRequest="40"
                                    HeightRequest="40"
                                    VerticalOptions="Center">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="20" />
                                </Border.StrokeShape>
                                <Path Fill="#059669" 
                                      VerticalOptions="Center"
                                      HorizontalOptions="Center"
                                      Data="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </Border>
                            
                            <VerticalStackLayout Grid.Column="1" 
                                                 Margin="12,0"
                                                 VerticalOptions="Center"
                                                 Spacing="2">
                                <Label FontSize="16" 
                                       FontAttributes="Bold" 
                                       Text="Manage Referral Codes" 
                                       TextColor="#111827" />
                                <Label FontSize="14" 
                                       Text="Create and share friend invitation codes" 
                                       TextColor="#6B7280" />
                            </VerticalStackLayout>
                            
                            <Path Grid.Column="2" 
                                  Fill="#9CA3AF" 
                                  VerticalOptions="Center"
                                  Data="M9 5l7 7-7 7" />
                                  
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding NavigateToReferralCodesCommand}" />
                            </Grid.GestureRecognizers>
                        </Grid>
                    </Border>

                    <!-- Logout -->
                    <Border BackgroundColor="White" StrokeThickness="0" Margin="0,8,0,0">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="0" />
                        </Border.StrokeShape>
                        <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                            <Border Grid.Column="0"
                                    BackgroundColor="#FEF2F2"
                                    WidthRequest="40"
                                    HeightRequest="40"
                                    VerticalOptions="Center">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="20" />
                                </Border.StrokeShape>
                                <Path Fill="#DC2626" 
                                      VerticalOptions="Center"
                                      HorizontalOptions="Center"
                                      Data="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </Border>
                            
                            <VerticalStackLayout Grid.Column="1" 
                                                 Margin="12,0"
                                                 VerticalOptions="Center"
                                                 Spacing="2">
                                <Label FontSize="16" 
                                       FontAttributes="Bold" 
                                       Text="Logout" 
                                       TextColor="#111827" />
                                <Label FontSize="14" 
                                       Text="Sign out of your account" 
                                       TextColor="#6B7280" />
                            </VerticalStackLayout>
                            
                            <Path Grid.Column="2" 
                                  Fill="#9CA3AF" 
                                  VerticalOptions="Center"
                                  Data="M9 5l7 7-7 7" />
                                  
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding LogoutCommand}" />
                            </Grid.GestureRecognizers>
                        </Grid>
                    </Border>
                </VerticalStackLayout>

                <!-- Bottom Spacing -->
                <BoxView HeightRequest="32" Color="Transparent" />
            </VerticalStackLayout>
        </ScrollView>

        <!-- Loading State -->
        <VerticalStackLayout Grid.Row="1" 
                             IsVisible="{Binding IsWorking}" 
                             VerticalOptions="Center" 
                             HorizontalOptions="Center"
                             Spacing="16">
            <ActivityIndicator IsRunning="{Binding IsWorking}" Color="#004f98" />
            <Label Text="Loading profile..." 
                   FontSize="16" 
                   TextColor="#6B7280"
                   HorizontalTextAlignment="Center" />
        </VerticalStackLayout>

        <!-- Empty State -->
        <VerticalStackLayout Grid.Row="1" 
                             IsVisible="{Binding IsEmptyState}" 
                             VerticalOptions="Center" 
                             HorizontalOptions="Center"
                             Spacing="16"
                             Padding="32">
            <Path Fill="#D1D5DB" 
                  WidthRequest="64" 
                  HeightRequest="64"
                  Data="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            <Label Text="Profile not found" 
                   FontSize="18" 
                   FontAttributes="Bold" 
                   TextColor="#374151"
                   HorizontalTextAlignment="Center" />
            <Label Text="Unable to load your profile information. Please try again." 
                   FontSize="14" 
                   TextColor="#6B7280"
                   HorizontalTextAlignment="Center"
                   LineBreakMode="WordWrap"
                   MaxLines="3" />
            <Button BackgroundColor="#004f98"
                    TextColor="White"
                    Text="Retry"
                    FontSize="16"
                    Command="{Binding RefreshCommand}"
                    Padding="24,12"
                    CornerRadius="8" />
        </VerticalStackLayout>
    </Grid>
</local:ProfileListingViewBase>
