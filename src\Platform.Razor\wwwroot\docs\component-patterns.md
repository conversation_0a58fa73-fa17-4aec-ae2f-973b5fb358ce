# DeepMessage Component Patterns

This document defines reusable component patterns using the integrated Tailwind + CSS Variables theme system.

## Design Principles

- Use semantic Tailwind classes that reference CSS variables
- Maintain Nothing Phone aesthetic (blacks, whites, grays)
- Ensure mobile-first responsive design
- Support runtime theme switching
- Follow consistent spacing and typography patterns

## Core Patterns

### 1. Header Patterns

#### Main Header
```razor
<header class="bg-header border-b border-border px-4 py-3 shadow-theme-sm">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <!-- Header content -->
        </div>
        <div class="flex items-center space-x-1">
            <!-- Action buttons -->
        </div>
    </div>
</header>
```

#### Chat Header
```razor
<header class="bg-header border-b border-border px-4 py-3 shadow-theme-sm">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <!-- Back button -->
            <button class="p-2 text-primary rounded-full hover:bg-muted transition-theme focus-theme">
                <!-- Back icon -->
            </button>
            
            <!-- Avatar -->
            <div class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                <span class="text-primary font-medium text-lg"><!-- Initials --></span>
            </div>
            
            <!-- User info -->
            <div>
                <h1 class="text-lg font-semibold text-primary"><!-- Name --></h1>
                <p class="text-sm text-secondary"><!-- Status --></p>
            </div>
        </div>
        
        <!-- Action buttons -->
        <div class="flex items-center space-x-1">
            <button class="p-2 text-secondary hover:text-primary hover:bg-muted rounded-full transition-theme focus-theme">
                <!-- Action icon -->
            </button>
        </div>
    </div>
</header>
```

### 2. Navigation Patterns

#### Bottom Tab Navigation
```razor
<nav class="bg-nav border-t border-border shadow-theme-sm">
    <div class="flex justify-around py-2">
        <button class="flex flex-col items-center py-2 px-4 text-secondary hover:text-primary transition-theme focus-theme">
            <!-- Icon -->
            <span class="text-xs mt-1"><!-- Label --></span>
        </button>
        
        <!-- Active tab -->
        <button class="flex flex-col items-center py-2 px-4 text-primary bg-muted rounded-lg transition-theme">
            <!-- Icon -->
            <span class="text-xs mt-1 font-semibold"><!-- Label --></span>
        </button>
    </div>
</nav>
```

### 3. Form Patterns

#### Message Input Form
```razor
<div class="bg-surface border-t border-border p-4 shadow-theme-sm">
    <div class="relative">
        <textarea 
            placeholder="Type a message..."
            class="block w-full px-4 py-3 pr-12 border border-border rounded-xl bg-surface text-primary placeholder:text-tertiary focus-theme transition-theme resize-none"
            style="min-height: 32px; max-height: 120px; overflow-y: auto;">
        </textarea>
        
        <!-- Send button -->
        <button class="absolute right-2 bottom-2 p-3 h-10 w-10 bg-gray-800 hover:bg-gray-900 disabled:bg-gray-300 text-inverse rounded-full disabled:cursor-not-allowed transition-theme focus-theme">
            <!-- Send icon -->
        </button>
    </div>
</div>
```

#### Search Input
```razor
<div class="relative">
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-4 w-4 text-tertiary"><!-- Search icon --></svg>
    </div>
    <input 
        type="text"
        placeholder="Search messages..."
        class="w-full pl-10 pr-3 py-2 bg-surface border border-border rounded-lg text-primary placeholder:text-tertiary focus-theme text-sm transition-theme" />
</div>
```

### 4. Dialog Patterns

#### Modal Dialog
```razor
<!-- Backdrop -->
<div class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-theme"></div>

<!-- Modal Container -->
<div class="fixed inset-0 z-50 flex items-center justify-center p-2">
    <div class="relative overflow-hidden rounded-xl bg-dialog shadow-theme-lg border border-border w-full max-w-md">
        
        <!-- Header -->
        <div class="bg-header border-b border-border px-4 py-3 sm:px-6 sm:py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-base font-semibold text-primary sm:text-lg">
                    <!-- Dialog Title -->
                </h3>
                <button class="ml-auto flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-secondary hover:bg-muted hover:text-primary transition-theme focus-theme">
                    <!-- Close icon -->
                </button>
            </div>
        </div>
        
        <!-- Body -->
        <div class="max-h-[calc(100vh-8rem)] sm:max-h-[calc(100vh-6rem)] overflow-y-auto bg-surface">
            <!-- Dialog content -->
        </div>
    </div>
</div>
```

### 5. Message Bubble Patterns

#### Sent Message
```razor
<div class="flex justify-end mb-4">
    <div class="max-w-xs lg:max-w-md">
        <div class="bg-bubble-sent rounded-2xl rounded-br-md px-4 py-2 shadow-theme-sm">
            <p class="text-primary text-sm"><!-- Message content --></p>
            <div class="flex items-center justify-end mt-1 space-x-1">
                <span class="text-xs text-secondary"><!-- Timestamp --></span>
                <!-- Read status icons -->
            </div>
        </div>
    </div>
</div>
```

#### Received Message
```razor
<div class="flex justify-start mb-4">
    <div class="max-w-xs lg:max-w-md">
        <div class="bg-bubble-received rounded-2xl rounded-bl-md px-4 py-2 shadow-theme-sm border border-border">
            <p class="text-primary text-sm"><!-- Message content --></p>
            <div class="flex items-center justify-end mt-1">
                <span class="text-xs text-secondary"><!-- Timestamp --></span>
            </div>
        </div>
    </div>
</div>
```

### 6. Loading and Error Patterns

#### Loading State
```razor
<div class="flex items-center justify-center py-16 w-full">
    <div class="flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-2 border-gray-300 border-t-gray-600"></div>
        <span class="text-secondary">Loading...</span>
    </div>
</div>
```

#### Error State
```razor
<div class="p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
        <svg class="h-5 w-5 text-red-600 mr-2"><!-- Error icon --></svg>
        <span class="text-sm text-red-800"><!-- Error message --></span>
    </div>
</div>
```

## Responsive Breakpoints

- **Mobile**: Default styles (mobile-first)
- **Tablet**: `sm:` prefix (640px+)
- **Desktop**: `lg:` prefix (1024px+)

## Accessibility Guidelines

- Always include `focus-theme` class on interactive elements
- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure color contrast meets WCAG AA standards
- Support keyboard navigation

## Theme Switching

All patterns automatically support light/dark mode through CSS variables. No component changes needed when switching themes.

## Common Utility Combinations

- **Interactive Element**: `transition-theme focus-theme`
- **Card/Surface**: `bg-surface border border-border rounded-lg shadow-theme-sm`
- **Button Primary**: `bg-gray-800 hover:bg-gray-900 text-inverse transition-theme focus-theme`
- **Button Secondary**: `text-secondary hover:text-primary hover:bg-muted transition-theme focus-theme`
