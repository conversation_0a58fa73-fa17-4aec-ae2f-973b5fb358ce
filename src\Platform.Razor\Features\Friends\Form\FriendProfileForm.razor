@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Friends
@using Platform.Client.Services.Features.Friends
@using Platform.Framework.Core
@using Platform.Razor.Components
@using Platform.Razor.Features.Profile.Form
@using Platform.Razor.Components.Avatar
@using System.Security.Claims
@page "/friends/profile/{Id}"
@inherits FormBase<FriendProfileFormBusinessObject, FriendProfileFormViewModel, string, IFriendProfileFormDataService>

@if (SelectedItem != null)
{
    <!-- Friend Profile Form - Tailwind CSS Design -->
    <EditForm Model=SelectedItem tem class="h-screen bg-gray-50 flex flex-col" OnInvalidSubmit="HandleFormSubmit">
        <DataAnnotationsValidator />

        <!-- Fixed Header -->
        <div class="bg-white border-b border-gray-200 px-4 py-3 flex-shrink-0 sticky top-0 z-10">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="/friends" class="inline-flex items-center justify-center w-8 h-8 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">Edit Friend</h1>
                </div>
                <button @onclick="HandleFormSubmit"
                        disabled="@IsWorking" type="submit"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    @if (IsSaving)
                    {
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Saving...</span>
                    }
                    else
                    {
                        <span>Save</span>
                    }
                </button>
            </div>
        </div>
        <!-- Error Message -->
        @if (!string.IsNullOrEmpty(Error))
        {
            <div class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 mt-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-red-700">@Error</p>
                    </div>
                </div>
            </div>
        }

        <!-- Scrollable Content -->
        <div class="flex-1 overflow-y-auto">
            <!-- Profile Display Section -->
            <div class="bg-white border-b border-gray-200 p-4">
                <div class="flex items-start space-x-4">
                    <!-- Avatar Display -->
                    <div class="flex-shrink-0">
                        <AvatarDisplay AvatarData="@SelectedItem.AvatarData"
                                       DisplayName="@SelectedItem.FriendName"
                                       Size="AvatarDisplay.AvatarSize.ExtraLarge"
                                       IsClickable="false" />
                    </div>

                    <!-- Name and Tagline -->
                    <div class="flex-1 min-w-0">
                        <h2 class="text-lg font-semibold text-gray-900 truncate">
                            @(SelectedItem?.FriendName ?? "No Name")
                        </h2>
                        <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                            @(SelectedItem.Tagline ?? "No Tagline")
                        </p>
                    </div>
                </div>
            </div>


            <!-- Form Fields Section -->
            <div class="p-4 space-y-6">
                <FieldSet Label="Select Avatar" HelpText="Choose from predefined avatar images">
                    <select @bind="SelectedItem.AvatarData" class="form-control">
                        <option value="">Choose an avatar...</option>
                        @for (int i = 1; i <= 12; i++)
                        {
                            <option value="/avatars/@(i).png">Avatar @i</option>
                        }
                    </select>
                </FieldSet>

                <FieldSet Label="Friend Name" HelpText="This is how your friend will appear in your contact list">
                    <InputText @bind-Value="SelectedItem.FriendName" class="form-control" placeholder="Enter friend's name" />
                </FieldSet>

                <FieldSet Label="Tagline" HelpText="Add a personal note about your friend">
                    <InputText @bind-Value="SelectedItem.FriendName" class="form-control" placeholder="Enter a tag line" />
                </FieldSet>
            </div>

            <!-- Bottom padding for better scrolling -->
            <div class="h-4 pb-16"></div>
        </div>
    </EditForm>
}

