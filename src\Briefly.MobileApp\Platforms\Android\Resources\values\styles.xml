﻿<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
	<!-- Base Application Theme -->
	<style name="MainTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
		<!-- Primary App Colors -->
		<item name="android:colorPrimary">#444444</item>
		<item name="android:colorPrimaryDark">#222222</item>
		<item name="android:colorAccent">#333333</item>
 
		<!-- Force Dark Mode Off (Enable if you want a fixed light theme) -->
		<item name="android:forceDarkAllowed">false</item>

		<!-- Set Status Bar Icons to Light (for dark-colored status bars) -->
		<item name="android:windowLightStatusBar">true</item>

		<!-- Set Navigation Bar Icons to Light (for dark-colored navigation bars) -->
		<item name="android:windowLightNavigationBar">true</item>
 
	</style>
</resources>
