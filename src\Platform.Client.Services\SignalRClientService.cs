﻿using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Platform.Client.Services.Features.Conversation;
using Platform.Framework.Core;
using System.Text.Json;
using System.Threading.Channels;
using Message = DeepMessage.Client.Common.Data.Message;

namespace DeepMessage.MauiApp.Services;
public class SignalRClientService : IAsyncDisposable
{
    private HubConnection? _hubConnection;
    private CancellationTokenSource _cts = new CancellationTokenSource();

    // These dependencies should be resolved via DI.
    private IServiceScopeFactory? _scopeFactory;
    private ILogger<SignalRClientService> _logger = null!;
    Task? _monitoringTask;
    Task? _monitoringProducerTask;

    string _chatHubUrl = string.Empty;

    public SignalRClientService(ILogger<SignalRClientService> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
    }

    public void Start(string chatHubUrl)
    {
        _chatHubUrl = chatHubUrl;

        //_syncDownTask = Task.Factory.StartNew(StartDownSyncTask, TaskCreationOptions.RunContinuationsAsynchronously);
        if (_monitoringTask == null || _monitoringTask.Status != TaskStatus.Running)
        {
            _monitoringTask = Task.Factory.StartNew(StartMonitor, TaskCreationOptions.RunContinuationsAsynchronously);
            _monitoringProducerTask = Task.Factory.StartNew(() =>
            {
                while (!_cts.IsCancellationRequested)
                {
                    try
                    {
                        _monitoringChannel.Writer.TryWrite(string.Empty);
                        _logger.LogDebug("Monitoring task running...");
                        Thread.Sleep(15000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in monitoring task: {Error}", ex.Message);
                    }
                }
            });
        }
        //_downSyncChannel.Writer.WriteAsync(new ChatSyncItem() { Id = string.Empty, SyncType = 1 }, _cts.Token);

    }

    private async Task StartMonitor()
    {
        while (!_cts.IsCancellationRequested)
        {
            var hint = await _monitoringChannel.Reader.ReadAsync();
            await StartSignalRConnection(_cts.Token);
            await Task.Delay(5000);
        }
    }

    Channel<string> _monitoringChannel = Channel.CreateBounded<string>(10);

    private bool IsAlive(object? obj)
    {
        try
        {
            if (obj != null && obj.ToString() != null)
            {
                _logger.LogDebug("IsAlive: true");
                return true;
            }
            _logger.LogDebug("IsAlive: false");
            return false;
        }
        catch (Exception ex)
        {

            _logger.LogError("IsAlive error: {Error}", ex.Message);
            return false;
        }
    }

    private async Task StartSignalRConnection(CancellationToken token)
    {
        try
        {
            _logger.LogDebug("SignalR Starting connection...");
            using var scope = _scopeFactory!.CreateScope();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            var httpHandler = new HttpClientHandler
            {
                // WARNING: Do not use this in production.
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };

            if (_hubConnection != null && IsAlive(_hubConnection) && _hubConnection?.State == HubConnectionState.Disconnected)
            {
                _logger.LogInformation("Stopping existing SignalR connection...");
                try
                {
                    await _hubConnection?.StopAsync();
                    _hubConnection?.DisposeAsync();
                    _hubConnection = null;
                }
                catch { }

            }


            if (_hubConnection == null || !IsAlive(_hubConnection))
            {

                _logger.LogDebug("SignalR Building connection...");
                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(_chatHubUrl, options =>
                    {
                        options.HttpMessageHandlerFactory = _ => httpHandler;
                        options.AccessTokenProvider = async () => await localStorageService.GetValue("auth_token");
                    })
                    .WithAutomaticReconnect(new[]
                    {
                    TimeSpan.Zero,
                    TimeSpan.FromSeconds(2),
                    TimeSpan.FromSeconds(10),
                    TimeSpan.FromSeconds(30)
                    })
                    .Build();

                _hubConnection.On<string, string>("OnNewFeed", ProcessIncomingMessage);

                _hubConnection.On("Logout", async () =>
                {
                    _logger.LogInformation("SignalR Logout event received.");
                    var scope = _scopeFactory.CreateScope();
                    var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                    await storageService.RemoveValue("auth_token");

                    //WeakReferenceMessenger.Default.Send("Logout");
                });


                _hubConnection.On<string, string>("OnFeedUpdate", ProcessMessageUpdate);

                _hubConnection.Reconnecting += error =>
                {
                    _logger?.LogWarning("SignalR reconnecting: {Error}", error?.Message);
                    return Task.CompletedTask;
                };

                _hubConnection.Reconnected += connectionId =>
                {
                    _logger?.LogInformation("SignalR reconnected. ConnectionId: {ConnectionId}", connectionId);
                    return Task.CompletedTask;
                };

                _hubConnection.Closed += async error =>
                {
                    _logger?.LogError("SignalR closed: {Error}", error?.Message);
                    await Task.Delay(TimeSpan.FromSeconds(5), token);
                    await _monitoringChannel.Writer.WriteAsync(string.Empty, token);
                };

                await _hubConnection.StartAsync(token);
                _logger.LogInformation("SignalR connection started...");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex.ToString());
            await _monitoringChannel.Writer.WriteAsync(string.Empty);
        }
    }

    // Process incoming SignalR messages.
    private async Task ProcessIncomingMessage(string userId, string messageJson)
    {
        try
        {
            _logger.LogInformation("SignalR Received broadcast: {0}", messageJson);
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessagesSyncFormBusinessObject>(messageJson);
            if (!string.IsNullOrEmpty(userId) && formBusinessObject != null)
            {
                // Create a new scope for the DbContext to keep it short-lived.
                using var scope = _scopeFactory.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                var message = await dbContext.Messages.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
                if (message == null)
                {
                    message = new Message();
                    message.Id = formBusinessObject.Id;
                    message.ConversationId = formBusinessObject.ConversationId;
                    message.CreatedAt = formBusinessObject.CreatedAt;
                    message.SenderId = formBusinessObject.SenderId;
                    dbContext.Messages.Add(message);
                }

                message.DeletedAt = formBusinessObject.DeletedAt;
                message.DisappearAfter = formBusinessObject.DisappearAfter;
                message.DisappearAt = formBusinessObject.DisappearAt;
                message.IsDeleted = formBusinessObject.IsDeleted;
                message.IsEdited = formBusinessObject.IsEdited;
                message.IsEphemeral = formBusinessObject.IsEphemeral;
                message.EditedAt = formBusinessObject.EditedAt;
                await dbContext.SaveChangesAsync();

                foreach (var messageRecipient in formBusinessObject.MessageRecipients)
                {
                    var recipient = await dbContext.MessageRecipients.FirstOrDefaultAsync(x => x.Id == messageRecipient.Id);
                    if (recipient == null)
                    {
                        recipient = new MessageRecipient
                        {
                            Id = messageRecipient.Id,
                            MessageId = message.Id,
                            RecipientId = messageRecipient.RecipientId,
                            DeliveryAcknowledgementStatus =  messageRecipient.RecipientId == userId? AcknowledgementStatus.QueuedToUpSync : 0,
                        };
                        dbContext.MessageRecipients.Add(recipient);
                    }
                    recipient.EncryptedContent = messageRecipient.EncryptedContent;
                    recipient.MessageDeliveryStatus = messageRecipient.DeliveryStatus;
                }

                await dbContext.SaveChangesAsync();

                if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                    await _hubConnection.SendAsync("AcknowledgeMessage", message.Id, userId, MessageDeliveryStatus.DeliveredToEndUser, DateTime.UtcNow);

                _logger.LogDebug("Added message: {0}", message.Id);
                PubSub.Hub.Default.Publish("NewMessageReceived");

            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error processing broadcast: {Error}", ex.Message);
        }
    }

    public async Task AcknowledgeMessageRead(string messageId, string recipientId, DateTime timeStamp)
    {
        if (_hubConnection == null || _hubConnection.State != HubConnectionState.Connected)
        {
            throw new InvalidOperationException("SignalR connection not available");
        }

        _logger.LogDebug("Sending direct acknowledgment for message: {MessageId}", messageId);

        // Send acknowledgment with timeout
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
        await _hubConnection.SendAsync("AcknowledgeMessage", messageId, recipientId, MessageDeliveryStatus.ReadByEndUser, timeStamp, cts.Token);

        // Update local database
        using var scope = _scopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var rowsAffected = await dbContext.Messages.Where(x => x.Id == messageId)
            .ExecuteUpdateAsync(m => m.SetProperty(x => x.DeliveryStatus, MessageDeliveryStatus.ReadByEndUser)
                                     .SetProperty(x => x.DeliveryStatusTime, timeStamp));

        if (rowsAffected == 0)
        {
            _logger.LogWarning("No rows updated for direct message acknowledgment: {MessageId}", messageId);
        }
        else
        {
            _logger.LogDebug("Successfully sent direct acknowledgment for message: {MessageId}", messageId);
        }
    }

    public async Task ProcessMessageUpdate(string userId, string messageJson)
    {
        try
        {
            //todo: check if userId matches the current user
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessageUpdate>(messageJson);
            if (!string.IsNullOrEmpty(userId) && formBusinessObject != null)
            {

                try
                {
                    using var scope = _scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    await context.Messages.Where(x => x.Id == formBusinessObject.MessageId)
                         .ExecuteUpdateAsync(m => m.SetProperty(x => x.DeliveryStatus, formBusinessObject.DeliveryStatus)
                                                  .SetProperty(x => x.DeliveryStatusTime, formBusinessObject.DeliveryStatusTime));

                    await context.MessageRecipients.Where(x => x.MessageId == formBusinessObject.MessageId && x.RecipientId == formBusinessObject.SenderId)
                        .ExecuteUpdateAsync(m => m.SetProperty(x => x.MessageDeliveryStatus, formBusinessObject.DeliveryStatus)
                                                 .SetProperty(x => x.DeliveryAcknowledgementStatus, AcknowledgementStatus.SentToEndUserViaSignalR));

                    WeakReferenceMessenger.Default.Send(new ChatMessageStatus()
                    {
                        Id = formBusinessObject.MessageId,
                        DeliveryStatus = formBusinessObject.DeliveryStatus,
                        Timestamp = formBusinessObject.DeliveryStatusTime
                    });

                    _logger?.LogDebug("Updated message {MessageId} status to {Status}", formBusinessObject.MessageId, formBusinessObject.DeliveryStatus);
                }
                catch (Exception ex)
                {
                    _logger?.LogError("Error updating message: {Error}", ex.Message);
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error updating message: {Error}", ex.Message);
        }
    }

    public async ValueTask DisposeAsync()
    {
        _cts.Cancel();
        if (_hubConnection != null)
        {
            await _hubConnection.StopAsync();
            await _hubConnection.DisposeAsync();
            _hubConnection = null;
        }
    }
}




