using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace DeepMessage.Server.DataServices.Services
{
    public interface IPerformanceMonitoringService
    {
        void TrackMessageDelivery(string messageId, TimeSpan duration, bool success);
        void TrackSignalRConnection(string userId, bool connected);
        void TrackDatabaseOperation(string operation, TimeSpan duration);
        void TrackApiRequest(string endpoint, TimeSpan duration, int statusCode);
        PerformanceMetrics GetMetrics();
    }

    public class PerformanceMonitoringService : IPerformanceMonitoringService
    {
        private readonly ILogger<PerformanceMonitoringService> _logger;
        private readonly ConcurrentDictionary<string, PerformanceCounter> _counters;
        private readonly Timer _metricsTimer;

        public PerformanceMonitoringService(ILogger<PerformanceMonitoringService> logger)
        {
            _logger = logger;
            _counters = new ConcurrentDictionary<string, PerformanceCounter>();
            
            // Log metrics every 5 minutes
            _metricsTimer = new Timer(LogMetrics, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        public void TrackMessageDelivery(string messageId, TimeSpan duration, bool success)
        {
            var counter = _counters.GetOrAdd("message_delivery", _ => new PerformanceCounter());
            counter.Increment(duration, success);

            if (duration > TimeSpan.FromSeconds(5))
            {
                _logger.LogWarning("Slow message delivery detected. MessageId: {MessageId}, Duration: {Duration}ms", 
                    messageId, duration.TotalMilliseconds);
            }

            if (!success)
            {
                _logger.LogError("Message delivery failed. MessageId: {MessageId}", messageId);
            }
        }

        public void TrackSignalRConnection(string userId, bool connected)
        {
            var counter = _counters.GetOrAdd("signalr_connections", _ => new PerformanceCounter());
            counter.Increment(TimeSpan.Zero, connected);

            _logger.LogInformation("SignalR connection {Status} for user {UserId}", 
                connected ? "established" : "lost", userId);
        }

        public void TrackDatabaseOperation(string operation, TimeSpan duration)
        {
            var counter = _counters.GetOrAdd($"db_{operation}", _ => new PerformanceCounter());
            counter.Increment(duration, true);

            if (duration > TimeSpan.FromSeconds(2))
            {
                _logger.LogWarning("Slow database operation detected. Operation: {Operation}, Duration: {Duration}ms", 
                    operation, duration.TotalMilliseconds);
            }
        }

        public void TrackApiRequest(string endpoint, TimeSpan duration, int statusCode)
        {
            var counter = _counters.GetOrAdd($"api_{endpoint}", _ => new PerformanceCounter());
            counter.Increment(duration, statusCode < 400);

            if (duration > TimeSpan.FromSeconds(3))
            {
                _logger.LogWarning("Slow API request detected. Endpoint: {Endpoint}, Duration: {Duration}ms, StatusCode: {StatusCode}", 
                    endpoint, duration.TotalMilliseconds, statusCode);
            }
        }

        public PerformanceMetrics GetMetrics()
        {
            var metrics = new PerformanceMetrics
            {
                Timestamp = DateTime.UtcNow,
                Counters = new Dictionary<string, CounterMetrics>()
            };

            foreach (var kvp in _counters)
            {
                var counter = kvp.Value;
                metrics.Counters[kvp.Key] = new CounterMetrics
                {
                    TotalRequests = counter.TotalRequests,
                    SuccessfulRequests = counter.SuccessfulRequests,
                    FailedRequests = counter.FailedRequests,
                    AverageResponseTime = counter.AverageResponseTime,
                    MaxResponseTime = counter.MaxResponseTime,
                    MinResponseTime = counter.MinResponseTime
                };
            }

            return metrics;
        }

        private void LogMetrics(object? state)
        {
            try
            {
                var metrics = GetMetrics();
                _logger.LogInformation("Performance Metrics: {Metrics}", System.Text.Json.JsonSerializer.Serialize(metrics));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging performance metrics");
            }
        }

        public void Dispose()
        {
            _metricsTimer?.Dispose();
        }
    }

    public class PerformanceCounter
    {
        private readonly object _lock = new object();
        private long _totalRequests;
        private long _successfulRequests;
        private long _failedRequests;
        private double _totalResponseTime;
        private double _maxResponseTime;
        private double _minResponseTime = double.MaxValue;

        public long TotalRequests => _totalRequests;
        public long SuccessfulRequests => _successfulRequests;
        public long FailedRequests => _failedRequests;
        public double AverageResponseTime => _totalRequests > 0 ? _totalResponseTime / _totalRequests : 0;
        public double MaxResponseTime => _maxResponseTime;
        public double MinResponseTime => _minResponseTime == double.MaxValue ? 0 : _minResponseTime;

        public void Increment(TimeSpan duration, bool success)
        {
            lock (_lock)
            {
                _totalRequests++;
                
                if (success)
                    _successfulRequests++;
                else
                    _failedRequests++;

                var durationMs = duration.TotalMilliseconds;
                _totalResponseTime += durationMs;
                
                if (durationMs > _maxResponseTime)
                    _maxResponseTime = durationMs;
                
                if (durationMs < _minResponseTime)
                    _minResponseTime = durationMs;
            }
        }
    }

    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public Dictionary<string, CounterMetrics> Counters { get; set; } = new();
    }

    public class CounterMetrics
    {
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public long FailedRequests { get; set; }
        public double AverageResponseTime { get; set; }
        public double MaxResponseTime { get; set; }
        public double MinResponseTime { get; set; }
    }
}
