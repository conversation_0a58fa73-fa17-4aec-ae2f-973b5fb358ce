﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts;

namespace Platform.Client.Services.Features.Account;
public class ProfileClientSideFormDataService : IProfileFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public ProfileClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(ProfileFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/ProfilesForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<ProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<ProfileFormBusinessObject>($"api/ProfilesForm/GetItemById?id=" + id);
	}
}
