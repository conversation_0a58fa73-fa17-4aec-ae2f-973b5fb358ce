# Stealth Mode Authentication System

A sophisticated stealth-mode authentication system that provides a hidden authentication flow through a news interface. This system implements a dual-purpose authentication mechanism that automatically detects device registration status and routes users through appropriate authentication flows.

## System Overview

The stealth authentication system is designed to provide secure access while maintaining the appearance of a regular news application. Users can access the authentication system through a hidden trigger in the news search functionality.

### Key Components

1. **NewsListing** - Enhanced news interface with integrated stealth mode functionality
2. **FakeCaptchaScreen** - Realistic captcha interface for auth code input
3. **SignUpForm** - Enhanced signup form with stealth mode and friend code integration
4. **SignInForm** - Standard signin form with stealth mode compatibility
5. **LocalStorageService** - Secure browser storage management

## Consolidated Architecture

The system has been consolidated to eliminate duplicate components and align with the existing XAML application architecture. All stealth mode functionality is now integrated directly into the standard components.

## Authentication Flow

### 1. Initial Screen Logic

**Default Behavior:**
- Users start at the news screen (`/` or `/news`)
- News interface appears as a standard news aggregation site
- Search functionality is available for legitimate news searches

**Stealth Mode Detection:**
- When stealth mode is enabled in user settings (default: enabled)
- Users can enter a stealth mode code in the search field (default: `***`)
- The code is customizable through user settings
- Input field switches to password type when stealth code is detected

### 2. Stealth Mode Activation

**Trigger Mechanism:**
```
User enters stealth code ("***") → Search field becomes password field → Enter key activates stealth mode
```

**Security Features:**
- Stealth activation timestamp is stored for session validation
- 5-minute timeout for security (prevents unauthorized access)
- Visual feedback is minimal to maintain stealth appearance

### 3. Fake Captcha Interface

**Realistic Captcha Simulation:**
- Mimics Google reCAPTCHA interface with authentic styling
- Includes fake image selection challenges (30% probability)
- "I'm not a robot" checkbox with verification animation
- Proper loading states and visual feedback

**Auth Code Input:**
- Appears after captcha "verification"
- Dual-purpose input field for friend codes or passkeys
- Server determines the appropriate flow based on device registration

### 4. Dual-Purpose Authentication Logic

**Server-Side Flow Detection:**
```
Auth Code Submitted → Server checks device nickname → Routes to appropriate flow
```

**Unregistered Device (Signup Flow):**
- Server detects `nickname == null`
- Auth code is validated as a friend code
- User is redirected to signup screen with friend code pre-filled
- Friend code is consumed during registration process

**Registered Device (Signin Flow):**
- Server detects existing device registration
- Auth code is treated as a passkey/password
- JWT token is generated and user is authenticated
- Direct navigation to chat threads screen

## Technical Implementation

### Component Architecture

#### NewsListing Component
**Location:** `Platform.Razor/Features/Home/Listing/NewsListing.razor`

**Features:**
- Standard news listing functionality with search
- Integrated stealth mode trigger detection
- Debounced search with 500ms delay
- Responsive design with dark mode support
- Settings integration for stealth mode configuration
- Automatic authentication state checking

**Key Methods:**
- `OnSearchKeyUp()` - Detects stealth mode activation and handles normal search
- `ActivateStealthMode()` - Initiates stealth authentication flow
- `LoadStealthModeSettings()` - Loads user preferences
- `CheckAuthenticationRedirect()` - Handles authenticated user redirects

#### FakeCaptchaScreen Component
**Location:** `Platform.Razor/Features/Authentication/Captcha/FakeCaptchaScreen.razor`

**Features:**
- Realistic reCAPTCHA interface simulation
- Fake image selection challenges
- Auth code input with dual-purpose logic
- Session validation and security checks
- Proper error handling and user feedback

**Key Methods:**
- `StartCaptchaVerification()` - Initiates fake captcha process
- `OnAfterSaveAsync()` - Handles dual-purpose auth code logic
- `HandleSignupFlow()` - Routes to signup for unregistered devices
- `HandleSigninFlow()` - Completes signin for registered devices

#### SignUpForm Component
**Location:** `Platform.Razor/Features/Authentication/SignUp/SignUpForm.razor`

**Features:**
- Simplified signup form aligned with XAML architecture
- Integrated stealth mode and friend code support
- Core fields only: NickName and PassKey (matching XAML version)
- Automatic stealth mode detection and friend code integration
- Streamlined validation and user experience

**Key Methods:**
- `CheckStealthModeOrigin()` - Validates stealth mode access
- `OnAfterSaveAsync()` - Handles successful registration with stealth mode routing
- `CleanupStealthModeData()` - Removes stealth mode artifacts
- `CanSubmit()` - Validates form submission requirements

### Data Flow

#### Stealth Mode Activation Flow
```
News Screen → Stealth Code Entry → Captcha Screen → Auth Code Input → Flow Detection
```

#### Signup Flow (Unregistered Device)
```
Auth Code (Friend Code) → Signup Form → Registration → Chat Threads
```

#### Signin Flow (Registered Device)
```
Auth Code (Passkey) → Token Generation → Chat Threads
```

### Security Features

#### Session Management
- **Stealth Activation Timeout:** 5-minute window for security
- **Token Storage:** Secure JWT token management
- **Device Tracking:** Device fingerprinting for security monitoring
- **Session Cleanup:** Automatic cleanup of stealth mode artifacts

#### Validation and Verification
- **Friend Code Validation:** Server-side friend code verification
- **Email Availability:** Real-time email availability checking
- **Password Strength:** Comprehensive password strength validation
- **Input Sanitization:** XSS prevention and input validation

#### Privacy Protection
- **Stealth Appearance:** Maintains news site appearance
- **Minimal Visual Feedback:** Reduces detection risk
- **Secure Storage:** Encrypted local storage for sensitive data
- **Audit Logging:** Security event logging for monitoring

## Configuration

### Stealth Mode Settings

**Default Configuration:**
```javascript
{
  "stealthModeEnabled": true,
  "stealthModeCode": "***",
  "sessionTimeout": 300000, // 5 minutes
  "captchaChallengeRate": 0.3 // 30% chance
}
```

**Customizable Options:**
- Stealth mode enable/disable
- Custom stealth activation code
- Session timeout duration
- Captcha challenge frequency

### Storage Keys

**Local Storage Keys Used:**
- `stealth_mode_enabled` - Stealth mode configuration
- `stealth_mode_code` - Custom activation code
- `stealth_activation_time` - Activation timestamp
- `friend_code` - Friend code for signup flow
- `from_stealth_mode` - Stealth mode origin flag
- `auth_token` - JWT authentication token
- `refresh_token` - Token refresh mechanism
- `user_id` - User identification
- `username` - User display name

## API Integration

### Authentication Endpoints

**Captcha Verification:**
```
POST /api/auth/verify-captcha
{
  "authCode": "string",
  "deviceString": "string"
}

Response:
{
  "flowType": "signup|signin",
  "success": boolean,
  "message": "string",
  "authClaims": AuthorizationClaimsModel
}
```

**Friend Code Validation:**
```
POST /api/auth/validate-friend-code
{
  "friendCode": "string",
  "deviceString": "string"
}

Response:
{
  "valid": boolean,
  "message": "string"
}
```

**Device Registration Check:**
```
GET /api/auth/device-status?deviceString={deviceString}

Response:
{
  "isRegistered": boolean,
  "nickname": "string",
  "lastSeen": "datetime"
}
```

## User Experience

### Visual Design

**News Interface:**
- Clean, modern news aggregation design
- Responsive layout with mobile optimization
- Dark mode support with smooth transitions
- Professional appearance to maintain stealth

**Captcha Interface:**
- Authentic reCAPTCHA styling and behavior
- Realistic loading animations and transitions
- Proper error states and user feedback
- Familiar user interaction patterns

**Signup Interface:**
- Enhanced form with friend code integration
- Visual confirmation of friend code validity
- Progressive form validation with real-time feedback
- Professional design consistent with modern web applications

### Accessibility

**WCAG AA Compliance:**
- Full keyboard navigation support
- Screen reader compatibility with proper ARIA labels
- High contrast color schemes for readability
- Focus management and logical tab order

**Mobile Optimization:**
- Touch-friendly interface with appropriate target sizes
- Responsive design for all screen sizes
- Virtual keyboard optimization
- Gesture support for mobile interactions

## Error Handling

### Common Error Scenarios

**Invalid Stealth Code:**
- Silent failure with no visual indication
- Maintains news search functionality
- No error messages to preserve stealth

**Expired Session:**
- Automatic redirect to news screen
- Session cleanup and security logging
- User-friendly error recovery

**Invalid Friend Code:**
- Clear error message with recovery options
- Suggestion to contact invitation sender
- Graceful fallback to regular signup

**Network Failures:**
- Retry mechanisms with exponential backoff
- Offline capability where possible
- Clear error messages with recovery instructions

## Deployment Considerations

### Environment Configuration

**Production Settings:**
- HTTPS enforcement for all authentication flows
- Secure token storage and transmission
- Rate limiting for authentication attempts
- Monitoring and alerting for security events

**Development Settings:**
- Debug logging for authentication flows
- Test friend codes for development
- Mock captcha challenges for testing
- Development-specific error messages

### Security Hardening

**Authentication Security:**
- JWT token expiration and refresh
- Device fingerprinting and tracking
- Rate limiting and brute force protection
- Audit logging for security events

**Data Protection:**
- Encryption of sensitive data in storage
- Secure transmission of authentication data
- Privacy protection for user information
- GDPR compliance for data handling

## Monitoring and Analytics

### Security Monitoring

**Key Metrics:**
- Stealth mode activation frequency
- Failed authentication attempts
- Session timeout occurrences
- Device registration patterns

**Alert Conditions:**
- Unusual stealth mode activation patterns
- High failure rates for authentication
- Suspicious device registration attempts
- Security policy violations

### User Analytics

**Usage Patterns:**
- News interface engagement metrics
- Stealth mode usage statistics
- Authentication flow completion rates
- User journey analysis

**Performance Metrics:**
- Page load times for all components
- Authentication flow completion times
- Error rates and recovery success
- Mobile vs desktop usage patterns

## Troubleshooting

### Common Issues

**Stealth Mode Not Activating:**
1. Check stealth mode enabled setting
2. Verify correct activation code
3. Ensure JavaScript is enabled
4. Check browser local storage support

**Captcha Screen Not Loading:**
1. Verify stealth activation timestamp
2. Check session timeout settings
3. Ensure proper navigation flow
4. Verify component registration

**Friend Code Not Working:**
1. Validate friend code format
2. Check server-side validation
3. Verify device registration status
4. Ensure proper API connectivity

**Authentication Failures:**
1. Check network connectivity
2. Verify API endpoint configuration
3. Validate token storage and retrieval
4. Check authentication service status

### Debug Tools

**Browser Developer Tools:**
- Local storage inspection
- Network request monitoring
- Console error logging
- Component state debugging

**Server-Side Logging:**
- Authentication flow tracking
- Error logging and analysis
- Performance monitoring
- Security event logging

## Future Enhancements

### Planned Features

**Enhanced Security:**
- Biometric authentication integration
- Multi-factor authentication support
- Advanced device fingerprinting
- Behavioral analysis for security

**User Experience:**
- Customizable news categories
- Personalized content recommendations
- Advanced search functionality
- Social sharing capabilities

**Administrative Features:**
- Friend code management interface
- User registration monitoring
- Security analytics dashboard
- Configuration management tools

### Extensibility

**Plugin Architecture:**
- Custom authentication providers
- Third-party news sources
- Additional captcha challenges
- Custom stealth activation methods

**API Extensions:**
- GraphQL support for flexible queries
- Webhook integration for real-time updates
- OAuth provider integration
- Advanced analytics APIs
