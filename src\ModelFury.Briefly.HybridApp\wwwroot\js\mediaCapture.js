// Media Capture JavaScript Interop for MAUI Hybrid App
// This file provides JavaScript functions that will be called by the MediaCapture Razor component
// The actual implementation will be handled by MAUI platform-specific code

window.capturePhoto = async function () {
    try {
        // This function will be intercepted by MAUI platform-specific handlers
        // For web fallback, we can use the HTML5 camera API
        if (window.DotNet && window.DotNet.invokeMethodAsync) {
            // Call MAUI platform-specific method
            const result = await window.DotNet.invokeMethodAsync('ModelFury.Briefly.HybridApp', 'CapturePhotoNative');
            return result;
        } else {
            // Web fallback using HTML5 getUserMedia API
            return await capturePhotoWeb();
        }
    } catch (error) {
        console.error('Error capturing photo:', error);
        throw new Error('Failed to capture photo: ' + error.message);
    }
};

window.selectPhoto = async function () {
    try {
        // This function will be intercepted by MAUI platform-specific handlers
        if (window.DotNet && window.DotNet.invokeMethodAsync) {
            // Call MAUI platform-specific method
            const result = await window.DotNet.invokeMethodAsync('ModelFury.Briefly.HybridApp', 'SelectPhotoNative');
            return result;
        } else {
            // Web fallback using HTML5 file input
            return await selectPhotoWeb();
        }
    } catch (error) {
        console.error('Error selecting photo:', error);
        throw new Error('Failed to select photo: ' + error.message);
    }
};

// Web fallback implementation for camera capture
async function capturePhotoWeb() {
    return new Promise((resolve, reject) => {
        // Create a hidden file input for camera
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'camera'; // This hints to use camera on mobile devices
        input.style.display = 'none';
        
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Return base64 data
                    resolve(e.target.result);
                };
                reader.onerror = function() {
                    reject(new Error('Failed to read captured image'));
                };
                reader.readAsDataURL(file);
            } else {
                resolve(''); // User cancelled
            }
            
            // Clean up
            document.body.removeChild(input);
        };
        
        input.oncancel = function() {
            resolve(''); // User cancelled
            document.body.removeChild(input);
        };
        
        // Add to DOM and trigger click
        document.body.appendChild(input);
        input.click();
    });
}

// Web fallback implementation for photo selection
async function selectPhotoWeb() {
    return new Promise((resolve, reject) => {
        // Create a hidden file input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.style.display = 'none';
        
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Return base64 data
                    resolve(e.target.result);
                };
                reader.onerror = function() {
                    reject(new Error('Failed to read selected image'));
                };
                reader.readAsDataURL(file);
            } else {
                resolve(''); // User cancelled
            }
            
            // Clean up
            document.body.removeChild(input);
        };
        
        input.oncancel = function() {
            resolve(''); // User cancelled
            document.body.removeChild(input);
        };
        
        // Add to DOM and trigger click
        document.body.appendChild(input);
        input.click();
    });
}

// Helper function to check if running in MAUI context
window.isMauiContext = function() {
    return window.DotNet && window.DotNet.invokeMethodAsync;
};

// Helper function to check media capture capabilities
window.getMediaCapabilities = function() {
    const capabilities = {
        hasCamera: false,
        hasFileAccess: true,
        platform: 'web'
    };
    
    // Check for camera access
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        capabilities.hasCamera = true;
    }
    
    // In MAUI context, capabilities will be determined by platform
    if (window.isMauiContext()) {
        capabilities.platform = 'maui';
        capabilities.hasCamera = true; // Assume MAUI platforms have camera access
    }
    
    return capabilities;
};

// Initialize media capture when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Media capture JavaScript initialized');
    console.log('Capabilities:', window.getMediaCapabilities());
});
