﻿<?xml version="1.0" encoding="utf-8" ?>
<local:FriendFormViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    Title="Add a friend"
    x:DataType="local:FriendFormViewBase"
    BackgroundColor="White">
    <Grid
        Padding="20"
        ColumnDefinitions="*,*"
        RowDefinitions="Auto,Auto,Auto,*">

        <!--  Heading  -->
        <Label
            Grid.Row="0"
            Grid.ColumnSpan="2"
            Margin="0,0,0,10"
            FontAttributes="Bold"
            FontSize="20"
            Text="Add a Friend"
            TextColor="Black" />

        <!--  Nickname label + Required indicator  -->
        <VerticalStackLayout
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Margin="0,0,0,5"
            Spacing="2">
            <Label Text="Nickname" TextColor="Black" />
            <!--  Nickname Entry  -->
            <Entry
                Grid.Row="2"
                Grid.ColumnSpan="2"
                Margin="0,0,0,10"
                Placeholder="Enter nickname..."
                Text="{Binding SelectedItem.NickName}" />

        </VerticalStackLayout>


        <VerticalStackLayout
            Grid.Row="2"
            Grid.ColumnSpan="2"
            Margin="0,10,0,5"
            Spacing="2">
            <Label Text="Secret code" TextColor="Black" />
            <!--  secretvode Entry  -->
            <Entry
                Grid.Row="2"
                Grid.ColumnSpan="2"
                Margin="0,0,0,10"
                Placeholder="******"
                Text="{Binding SelectedItem.AuthCode}" />

        </VerticalStackLayout>

        <!--  Buttons (Right-Aligned)  -->
        <HorizontalStackLayout
            Grid.Row="3"
            Grid.ColumnSpan="2"
            HorizontalOptions="End"
            Spacing="10"
            VerticalOptions="Start">
            <Button
                x:Name="SaveButton"
                BackgroundColor="Black"
                Command="{Binding SaveCommand}"
                Text="Save"
                TextColor="White" />
            <Button
                x:Name="CancelButton"
                BackgroundColor="Gray"
                Text="Cancel"
                TextColor="White" />

        </HorizontalStackLayout>
    </Grid>
</local:FriendFormViewBase>
