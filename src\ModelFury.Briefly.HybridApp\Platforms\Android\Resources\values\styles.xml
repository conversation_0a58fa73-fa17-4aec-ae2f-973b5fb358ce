<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base Application Theme with No Action Bar -->
    <style name="MainTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Nothing Phone Black Primary Colors -->
        <item name="android:colorPrimary">#18181b</item>
        <item name="android:colorPrimaryDark">#09090b</item>
        <item name="android:colorAccent">#ff0000</item>

        <!-- Status Bar Configuration -->
        <!--<item name="android:statusBarColor">#18181b</item>-->
        <item name="android:windowLightStatusBar">true</item>
        
        <!-- Navigation Bar Configuration -->
        <!--<item name="android:navigationBarColor">#18181b</item>-->
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Window Configuration for Full Screen -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        
        <!-- Edge-to-Edge Configuration -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        
        <!-- Force Dark Mode Off -->
        <item name="android:forceDarkAllowed">false</item>
        
        <!-- Remove Window Title and Action Bar -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="MainTheme.Splash" parent="MainTheme">
        <!--<item name="android:windowBackground">@drawable/splash</item>-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
