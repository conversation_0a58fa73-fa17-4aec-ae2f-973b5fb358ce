<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Nothing Phone Gradient Definitions -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fafafa;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#f5f5f5;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#eeeeee;stop-opacity:0.4"/>
    </linearGradient>
    
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e0e0e0;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#bdbdbd;stop-opacity:0.2"/>
    </linearGradient>
    
    <radialGradient id="accentGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:0.05"/>
    </radialGradient>
    
    <!-- Subtle Pattern Definitions -->
    <pattern id="dotPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="20" r="1" fill="#9e9e9e" opacity="0.1"/>
    </pattern>
    
    <pattern id="linePattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
      <path d="M0,30 L60,30" stroke="#757575" stroke-width="0.5" opacity="0.08"/>
      <path d="M30,0 L30,60" stroke="#757575" stroke-width="0.5" opacity="0.08"/>
    </pattern>
  </defs>
  
  <!-- Base Background -->
  <rect width="400" height="600" fill="#ffffff"/>
  
  <!-- Subtle Pattern Overlay -->
  <rect width="400" height="600" fill="url(#dotPattern)"/>
  
  <!-- Abstract Geometric Shapes - Nothing Phone Style -->
  
  <!-- Large Background Circles -->
  <circle cx="100" cy="150" r="80" fill="url(#primaryGradient)" opacity="0.6"/>
  <circle cx="320" cy="400" r="120" fill="url(#secondaryGradient)" opacity="0.5"/>
  <circle cx="50" cy="500" r="60" fill="url(#primaryGradient)" opacity="0.4"/>
  
  <!-- Minimalistic Geometric Lines -->
  <path d="M0,200 Q200,180 400,220" stroke="#e0e0e0" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,350 Q150,330 400,360" stroke="#bdbdbd" stroke-width="1.5" fill="none" opacity="0.5"/>
  <path d="M0,480 Q250,460 400,490" stroke="#e0e0e0" stroke-width="1" fill="none" opacity="0.4"/>
  
  <!-- Abstract Polygons -->
  <polygon points="150,50 200,80 180,130 120,120" fill="url(#primaryGradient)" opacity="0.3"/>
  <polygon points="280,250 340,280 320,340 260,320" fill="url(#secondaryGradient)" opacity="0.4"/>
  <polygon points="80,350 130,370 110,420 60,400" fill="url(#primaryGradient)" opacity="0.3"/>
  
  <!-- Subtle Accent Elements -->
  <circle cx="350" cy="100" r="25" fill="url(#accentGradient)" opacity="0.8"/>
  <circle cx="80" cy="280" r="15" fill="url(#accentGradient)" opacity="0.6"/>
  
  <!-- Minimalistic Message Bubble Hints -->
  <ellipse cx="300" cy="180" rx="40" ry="20" fill="#f5f5f5" opacity="0.5" rx="40" ry="20"/>
  <ellipse cx="120" cy="420" rx="35" ry="18" fill="#eeeeee" opacity="0.6"/>
  
  <!-- Subtle Grid Lines -->
  <rect width="400" height="600" fill="url(#linePattern)" opacity="0.3"/>
  
  <!-- Organic Flow Lines -->
  <path d="M50,100 Q200,120 350,140 Q300,200 150,180 Q100,140 50,100" 
        fill="none" stroke="#e0e0e0" stroke-width="1" opacity="0.4"/>
  
  <path d="M100,300 Q250,320 380,340 Q330,400 180,380 Q130,340 100,300" 
        fill="none" stroke="#bdbdbd" stroke-width="0.8" opacity="0.3"/>
  
  <!-- Nothing Phone Signature Elements -->
  <rect x="20" y="20" width="2" height="40" fill="#9e9e9e" opacity="0.6"/>
  <rect x="378" y="540" width="2" height="40" fill="#9e9e9e" opacity="0.6"/>
  <rect x="20" y="560" width="40" height="2" fill="#9e9e9e" opacity="0.6"/>
  <rect x="340" y="20" width="40" height="2" fill="#9e9e9e" opacity="0.6"/>
</svg>
