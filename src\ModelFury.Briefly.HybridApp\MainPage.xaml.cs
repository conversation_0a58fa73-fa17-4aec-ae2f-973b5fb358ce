﻿using Microsoft.Maui.Platform;

namespace ModelFury.Briefly.HybridApp
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
            ConfigurePage();
        }

        
        private void ConfigurePage()
        {
            // Ensure no navigation bar or title bar is visible
            NavigationPage.SetHasNavigationBar(this, false);
            NavigationPage.SetTitleView(this, null);
            Shell.SetNavBarIsVisible(this, false);
            Shell.SetTabBarIsVisible(this, false);

            // Set Nothing Phone theme status bar color
            SetStatusBarColor(Colors.White);

            // Configure page background to match Nothing Phone theme
            //BackgroundColor = Colors.White;
        }

        void SetStatusBarColor(Color color)
        {
#if ANDROID
            var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window;

            if (window != null)
            {
                window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
                window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);
                window.SetStatusBarColor(color.ToPlatform());

                // Also set navigation bar color to match
                window.SetNavigationBarColor(color.ToPlatform());
            }
#elif IOS
            // iOS status bar configuration is handled in AppDelegate
            // But we can also configure it here if needed
            if (UIKit.UIApplication.SharedApplication != null)
            {
                UIKit.UIApplication.SharedApplication.SetStatusBarStyle(UIKit.UIStatusBarStyle.LightContent, false);
            }
#endif
        }
    }
}
