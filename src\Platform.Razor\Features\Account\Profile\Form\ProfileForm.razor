@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Account
@using Platform.Client.Services.Features.Account
@using Platform.Framework.Core
@using Platform.Razor.Components
@using Platform.Razor.Features.Profile.Form
@using Platform.Razor.Components.Avatar
@using System.Security.Claims 
@page "/profile/edit/{Id}"
@inherits FormBase<ProfileFormBusinessObject, ProfileFormViewModel, string, IProfileFormDataService>

@if (SelectedItem != null)
{
    <!-- Profile Form - Tailwind CSS Design -->
    <EditForm Model=SelectedItem class="h-screen bg-gray-50 flex flex-col" OnInvalidSubmit="HandleFormSubmit">
        <DataAnnotationsValidator />

        <!-- Fixed Header -->
        <div class="bg-white border-b border-gray-200 px-4 py-3 flex-shrink-0 sticky top-0 z-10">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="/dashboard" class="inline-flex items-center justify-center w-8 h-8 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">My Profile</h1>
                </div>
                <button @onclick="HandleFormSubmit"
                        disabled="@IsWorking" type="submit"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    @if (IsSaving)
                    {
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Saving...</span>
                    }
                    else
                    {
                        <span>Save</span>
                    }
                </button>
            </div>
        </div>
        <!-- Error Message -->
        @if (!string.IsNullOrEmpty(Error))
        {
            <div class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 mt-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-red-700">@Error</p>
                    </div>
                </div>
            </div>
        }

        <!-- Scrollable Content -->
        <div class="flex-1 overflow-y-auto">
            <!-- Profile Display Section -->
            <div class="bg-white border-b border-gray-200 p-4">
                <div class="flex items-start space-x-4">
                    <!-- Avatar Display -->
                    <div class="flex-shrink-0">
                        <AvatarDisplay AvatarData="@SelectedItem.AvatarData"
                                       DisplayName="@SelectedItem.DisplayName"
                                       Size="AvatarDisplay.AvatarSize.ExtraLarge"
                                       IsClickable="false" />
                    </div>

                    <!-- Name and Info -->
                    <div class="flex-1 min-w-0">
                        <h2 class="text-lg font-semibold text-gray-900 truncate">
                            @(SelectedItem?.DisplayName ?? "No Name")
                        </h2>
                        <p class="text-sm text-gray-600 mt-1">
                            Profile Information
                        </p>
                    </div>
                </div>
            </div>

            <!-- Form Fields Section -->
            <div class="p-4 space-y-6">
                <FieldSet Label="Select Avatar" HelpText="Choose from predefined avatar images or generate AI avatar">
                    <div class="space-y-3">
                        <select @bind="SelectedItem.AvatarData" class="form-control">
                            <option value="">Choose an avatar...</option>
                            @for (int i = 1; i <= 12; i++)
                            {
                                <option value="/avatars/@(i).png">Avatar @i</option>
                            }
                        </select>
                      @*   <button type="button" @onclick="OpenAvatarGenerator"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                            Generate AI Avatar
                        </button> *@
                    </div>
                </FieldSet>

                <FieldSet Label="Display Name" HelpText="This name will be visible to your friends when you add them as your friend">
                    <InputText @bind-Value="SelectedItem.DisplayName" class="form-control" placeholder="Enter your display name" />
                </FieldSet>
                 
            </div>

            <!-- Bottom padding for better scrolling -->
            <div class="h-4 pb-16"></div>
        </div>
    </EditForm>
}

@code { 

     

    private void OnDialogEvent(Tuple<string, string, dynamic> eventData)
    {
        if (eventData.Item2 == "Avatar Saved" || eventData.Item2 == "Avatar Selected")
        {
            // Refresh avatar data when avatar is saved or selected
            _ = Task.Run(async () =>
            { 
                await InvokeAsync(StateHasChanged);
            });
        }
    }

    private void OpenAvatarGenerator()
    {
        ShowDialog<AvatarForm>("Generate Avatar", null, Size.Md, Position_.Center, true);
    }
     
}
