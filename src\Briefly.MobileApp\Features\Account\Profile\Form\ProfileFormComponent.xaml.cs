﻿using ModelFury.Briefly.MobileApp.Features.Account;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Services.Features.Account;
using Microsoft.Extensions.DependencyInjection;
using System.Windows.Input;
using System.Collections.ObjectModel;
using System.Security.Claims;
using Platform.Framework.Core;

namespace ModelFury.Briefly.MobileApp.Features.Account;

public class ProfileFormViewBase : FormBaseMaui<ProfileFormBusinessObject, ProfileFormViewModel, string, IProfileFormDataService>
{
    public ProfileFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

[QueryProperty(nameof(ProfileId), "ProfileId")]
public partial class ProfileFormView : ProfileFormViewBase
{
    private string? profileId;
    public string? ProfileId
    {
        get => profileId;
        set => profileId = value;
    }

    // UI State Properties - Focus management removed, using platform defaults

    private bool hasError;
    public bool HasError
    {
        get => hasError;
        set
        {
            hasError = value;
            OnPropertyChanged();
        }
    }

    private bool hasSuccessMessage;
    public bool HasSuccessMessage
    {
        get => hasSuccessMessage;
        set
        {
            hasSuccessMessage = value;
            OnPropertyChanged();
        }
    }

    private string successMessage;
    public string SuccessMessage
    {
        get => successMessage;
        set
        {
            successMessage = value;
            OnPropertyChanged();
            HasSuccessMessage = !string.IsNullOrEmpty(value);
        }
    }

    private ObservableCollection<string> avatarOptions;
    public ObservableCollection<string> AvatarOptions
    {
        get => avatarOptions;
        set
        {
            avatarOptions = value;
            OnPropertyChanged();
        }
    }

    // Commands
    public ICommand BackCommand { get; }

    //public override Color TitleBarColor => Color.FromArgb("#004f98");

    public ProfileFormView(IServiceScopeFactory scopeFactory) : base(scopeFactory, null!)
    {
        InitializeComponent();
        BindingContext = this;

        BackCommand = new Command(async () => await GoBack());

        // Initialize avatar options
        InitializeAvatarOptions();
    }

    private void InitializeAvatarOptions()
    {
        AvatarOptions = new ObservableCollection<string>();
        for (int i = 1; i <= 12; i++)
        {
            AvatarOptions.Add($"/avatars/{i}.png");
        }
    }

    protected override async Task<ProfileFormViewModel> CreateSelectedItem()
    {
        var viewModel = new ProfileFormViewModel();
        
        try
        {
            // Get current user ID from storage or use the provided ProfileId
            using var scope = ScopeFactory.CreateScope();
            var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            
            var userId = !string.IsNullOrEmpty(ProfileId) && ProfileId != "current" 
                ? ProfileId 
                : await storageService.GetValue(ClaimTypes.NameIdentifier);

            if (!string.IsNullOrEmpty(userId))
            {
                viewModel.Id = userId;
                
                // Try to load existing profile data
                try
                {
                    using var dataScope = ScopeFactory.CreateScope();
                    var dataService = dataScope.ServiceProvider.GetRequiredService<IProfileFormDataService>();
                    var existingProfile = await dataService.GetItemByIdAsync(userId);
                    
                    if (existingProfile != null)
                    {
                        viewModel.DisplayName = existingProfile.DisplayName;
                        viewModel.AvatarData = existingProfile.AvatarData;
                        viewModel.AvatarDescription = existingProfile.AvatarDescription;
                    }
                }
                catch
                {
                    // If profile doesn't exist, start with empty form
                }
            }
        }
        catch (Exception ex)
        {
            HandleError($"Failed to load profile: {ex.Message}");
        }

        return viewModel;
    }

    private void HandleError(string error)
    {
        HasError = !string.IsNullOrEmpty(error);
        HasSuccessMessage = false;
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            await DisplayAlert("Error", error, "OK");
        });
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        try
        {
            ShowSuccessMessage("Profile updated successfully!");
            
            // Update user info in local storage if this is the current user's profile
            using var scope = ScopeFactory.CreateScope();
            var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var currentUserId = await storageService.GetValue(ClaimTypes.NameIdentifier);
            
            if (SelectedItem.Id == currentUserId && !string.IsNullOrEmpty(SelectedItem.DisplayName))
            {
                await storageService.SetValue(SelectedItem.DisplayName, ClaimTypes.Name);
            }

            // Sync profile data
            var profileSyncService = scope.ServiceProvider.GetRequiredService<Platform.Client.Services.Features.Account.IProfileSyncService>();
            await profileSyncService.SyncUserProfileAsync(SelectedItem.Id, SelectedItem.DisplayName);

            // Auto-navigate back after a short delay
            _ = Task.Delay(2000).ContinueWith(async _ =>
            {
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    await GoBack();
                });
            });
        }
        catch (Exception ex)
        {
            HandleError($"Profile saved but sync failed: {ex.Message}");
        }
    }

    private async Task GoBack()
    {
        try
        {
            if (Navigation.NavigationStack.Count > 1)
            {
                await Navigation.PopAsync();
            }
            else
            {
                await Shell.Current.GoToAsync("//profile");
            }
        }
        catch (Exception ex)
        {
            HandleError($"Failed to navigate back: {ex.Message}");
        }
    }

    private void ShowSuccessMessage(string message)
    {
        SuccessMessage = message;
        
        // Auto-hide success message after 5 seconds
        _ = Task.Delay(5000).ContinueWith(_ =>
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                SuccessMessage = string.Empty;
            });
        });
    }

    // Focus event handlers removed - using platform default focus behavior

    protected override void OnAppearing()
    {
        base.OnAppearing();
        
        // Clear any previous messages
        SuccessMessage = string.Empty;
        HasError = false;
    }
}
