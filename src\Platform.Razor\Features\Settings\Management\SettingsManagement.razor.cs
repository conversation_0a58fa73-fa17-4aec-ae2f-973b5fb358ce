using Microsoft.JSInterop;

namespace Platform.Razor.Features.Settings.Management
{
    public partial class SettingsManagement
    {
       
        // Appearance Settings
        private bool darkModeEnabled = false;
        private int fontSize = 16;

        // Notification Settings
        private bool messageNotificationsEnabled = true;
        private bool soundEnabled = true;

        // Stealth Mode Settings
        private bool stealthModeEnabled = true;
        private string stealthCode = "***";

        protected override async Task OnInitializedAsync()
        {
            await LoadSettings();
        }

        /// <summary>
        /// Loads settings from storage
        /// </summary>
        private async Task LoadSettings()
        {
            try
            {
                // Load appearance settings
                var darkMode = await StorageService.GetValue("dark_mode_enabled");
                if (!string.IsNullOrEmpty(darkMode))
                {
                    darkModeEnabled = bool.Parse(darkMode);
                }

                var fontSizeValue = await StorageService.GetValue("font_size");
                if (!string.IsNullOrEmpty(fontSizeValue) && int.TryParse(fontSizeValue, out var size))
                {
                    fontSize = size;
                }

                // Load notification settings
                var messageNotifications = await StorageService.GetValue("message_notifications_enabled");
                if (!string.IsNullOrEmpty(messageNotifications))
                {
                    messageNotificationsEnabled = bool.Parse(messageNotifications);
                }

                var sound = await StorageService.GetValue("sound_enabled");
                if (!string.IsNullOrEmpty(sound))
                {
                    soundEnabled = bool.Parse(sound);
                }

                // Load stealth mode settings
                var stealthMode = await StorageService.GetValue("stealth_mode_enabled");
                if (!string.IsNullOrEmpty(stealthMode))
                {
                    stealthModeEnabled = bool.Parse(stealthMode);
                }

                var stealthCodeValue = await StorageService.GetValue("stealth_mode_code");
                if (!string.IsNullOrEmpty(stealthCodeValue))
                {
                    stealthCode = stealthCodeValue;
                }
            }
            catch (Exception)
            {
                // Use default values if loading fails
            }
        }

        /// <summary>
        /// Saves all settings to storage
        /// </summary>
        private async Task SaveSettings()
        {
            try
            {
                // Save appearance settings
                await StorageService.SetValue(darkModeEnabled.ToString(), "dark_mode_enabled");
                await StorageService.SetValue(fontSize.ToString(), "font_size");

                // Save notification settings
                await StorageService.SetValue(messageNotificationsEnabled.ToString(), "message_notifications_enabled");
                await StorageService.SetValue(soundEnabled.ToString(), "sound_enabled");

                // Save stealth mode settings
                await StorageService.SetValue(stealthModeEnabled.ToString(), "stealth_mode_enabled");
                await StorageService.SetValue(stealthCode, "stealth_mode_code");

                // Apply dark mode immediately
                await ApplyDarkMode();

                // Apply font size immediately
                await ApplyFontSize();

                // Show success message
                await ShowToast("Settings saved successfully");
            }
            catch (Exception)
            {
                await ShowToast("Error saving settings");
            }
        }

        /// <summary>
        /// Toggles dark mode
        /// </summary>
        private async Task ToggleDarkMode()
        {
            await ApplyDarkMode();
            await StorageService.SetValue(darkModeEnabled.ToString(), "dark_mode_enabled");
        }

        /// <summary>
        /// Applies dark mode to the document
        /// </summary>
        private async Task ApplyDarkMode()
        {
            try
            {
                if (darkModeEnabled)
                {
                    await JSRuntime.InvokeVoidAsync("eval", "document.documentElement.classList.add('dark')");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("eval", "document.documentElement.classList.remove('dark')");
                }
            }
            catch
            {
                // Ignore errors
            }
        }

        /// <summary>
        /// Updates font size
        /// </summary>
        private async Task UpdateFontSize()
        {
            await ApplyFontSize();
            await StorageService.SetValue(fontSize.ToString(), "font_size");
        }

        /// <summary>
        /// Applies font size to the document
        /// </summary>
        private async Task ApplyFontSize()
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("eval", $"document.documentElement.style.fontSize = '{fontSize}px'");
            }
            catch
            {
                // Ignore errors
            }
        }

        /// <summary>
        /// Shows privacy settings (placeholder)
        /// </summary>
        private async Task ShowPrivacySettings()
        {
            await ShowToast("Privacy settings coming soon");
        }

        /// <summary>
        /// Signs out the user
        /// </summary>
        private async Task SignOut()
        {
            try
            {
                // Clear authentication data
                await StorageService.RemoveValue("auth_token");
                await StorageService.RemoveValue("refresh_token");
                await StorageService.RemoveValue("user_id");
                await StorageService.RemoveValue("username");

                // Clear other user data
                await StorageService.RemoveValue("unread_messages_count");
                await StorageService.RemoveValue("friend_requests_count");
                await StorageService.RemoveValue("selected_tab");

                // Navigate to home/news screen
                Navigation.NavigateTo("/", replace: true);
            }
            catch (Exception)
            {
                await ShowToast("Error signing out");
            }
        }

        /// <summary>
        /// Shows a toast message
        /// </summary>
        private async Task ShowToast(string message)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("eval", $@"
                    const toast = document.createElement('div');
                    toast.className = 'fixed top-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                    toast.textContent = '{message}';
                    document.body.appendChild(toast);
                    setTimeout(() => {{
                        toast.remove();
                    }}, 3000);
                ");
            }
            catch
            {
                // Ignore errors
            }
        }
    }
}
