// Global app functions
window.appFunctions = {
    // Add any global functions here
};

// Image Upload Helper Functions
window.triggerFileInput = function(element) {
    if (element) {
        element.click();
    }
};

// Toast notification system
window.showToast = function(message, type = 'info') {
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-600' : 
                   type === 'error' ? 'bg-red-600' : 
                   type === 'warning' ? 'bg-yellow-600' : 'bg-gray-800';
    
    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-0 opacity-100`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
};

// Image processing utilities
window.imageUtils = {
    // Validate image file
    validateImage: function(file, maxSizeKB = 2048) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        const maxSizeBytes = maxSizeKB * 1024;
        
        if (!validTypes.includes(file.type)) {
            return { valid: false, error: 'Invalid file type. Please select a JPG, PNG, or WebP image.' };
        }
        
        if (file.size > maxSizeBytes) {
            return { valid: false, error: `File size (${(file.size / 1024).toFixed(1)} KB) exceeds maximum allowed size (${maxSizeKB} KB).` };
        }
        
        return { valid: true };
    },
    
    // Create image preview
    createPreview: function(file, callback) {
        const reader = new FileReader();
        reader.onload = function(e) {
            callback(e.target.result);
        };
        reader.readAsDataURL(file);
    },
    
    // Resize image canvas
    resizeImage: function(file, maxWidth, maxHeight, quality, callback) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            // Calculate new dimensions
            let { width, height } = img;
            
            if (width > height) {
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }
            } else {
                if (height > maxHeight) {
                    width = (width * maxHeight) / height;
                    height = maxHeight;
                }
            }
            
            // Set canvas dimensions
            canvas.width = width;
            canvas.height = height;
            
            // Draw and compress
            ctx.drawImage(img, 0, 0, width, height);
            
            canvas.toBlob(callback, 'image/jpeg', quality / 100);
        };
        
        img.src = URL.createObjectURL(file);
    }
};

// Profile management utilities
window.profileUtils = {
    // Generate initials from name
    getInitials: function(name) {
        if (!name) return '?';
        
        const parts = name.split(' ').filter(part => part.length > 0);
        if (parts.length >= 2) {
            return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
        }
        
        return parts[0][0].toUpperCase();
    },
    
    // Validate display name
    validateDisplayName: function(name) {
        if (!name || name.trim().length === 0) {
            return { valid: false, error: 'Display name is required.' };
        }
        
        if (name.trim().length < 2) {
            return { valid: false, error: 'Display name must be at least 2 characters long.' };
        }
        
        if (name.trim().length > 50) {
            return { valid: false, error: 'Display name must be less than 50 characters long.' };
        }
        
        return { valid: true };
    }
};

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Briefly AI News app initialized');
    
    // Add any initialization code here
    
    // Set up global error handling
    window.addEventListener('error', function(e) {
        console.error('Global error:', e.error);
    });
    
    // Set up unhandled promise rejection handling
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled promise rejection:', e.reason);
    });
});
