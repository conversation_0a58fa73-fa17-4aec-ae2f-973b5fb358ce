# Required SVG Assets for XAML Implementation

This document lists all the Font Awesome SVG files that need to be added to the assets folder for the updated XAML implementation to work properly.

## Assets Folder Structure
All SVG files should be placed in the `assets/` folder of the MAUI project.

## Required SVG Files

**Total: 16 unique SVG files**

### FakeCaptchaScreen.xaml
1. **refresh_icon.svg** - Used for captcha refresh button
   - Font Awesome icon: `fa-refresh` or `fa-rotate-right`
   - SVG path data: `M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15`

2. **volume_icon.svg** - Used for audio captcha button
   - Font Awesome icon: `fa-volume-up` or `fa-speaker-wave`
   - SVG path data: `M15.536 11.293l-4.95-4.95M11 7l4 4-4 4`

3. **lock_icon.svg** - Used for auth code field icon and submit button
   - Font Awesome icon: `fa-lock`
   - SVG path data: `M15 7a2 2 0 012 2m5 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h1m2-4a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2h-4a2 2 0 01-2-2V5z`

4. **error_icon.svg** - Used for error display
   - Font Awesome icon: `fa-exclamation-circle`
   - SVG path data: `M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z`

### SignInFormComponent.xaml
5. **user_icon.svg** - Used for username field icon
   - Font Awesome icon: `fa-user` or `fa-at`
   - SVG path data: `M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207`

6. **eye_icon.svg** - Used for show password button
   - Font Awesome icon: `fa-eye`
   - SVG path data: `M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z`

7. **eye_off_icon.svg** - Used for hide password button
   - Font Awesome icon: `fa-eye-slash`
   - SVG path data: `M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21`

### FriendProfileFormComponent.xaml
8. **arrow_left_icon.svg** - Used for back button
   - Font Awesome icon: `fa-arrow-left`
   - SVG path data: `M15 19l-7-7 7-7`

### FriendsListingComponent.xaml
9. **refresh_spin_icon.svg** - Used for spinning refresh animation
   - Font Awesome icon: `fa-spinner` (animated)
   - SVG path data: `M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15` (with animation)

10. **plus_icon.svg** - Used for add friend button
    - Font Awesome icon: `fa-plus`
    - SVG path data: `M12 6v6m0 0v6m0-6h6m-6 0H6`

11. **search_icon.svg** - Used for search input field
    - Font Awesome icon: `fa-search`
    - SVG path data: `M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z`

12. **close_icon.svg** - Used for clear search button
    - Font Awesome icon: `fa-times`
    - SVG path data: `M6 18L18 6M6 6l12 12`

13. **check_icon.svg** - Used for success messages
    - Font Awesome icon: `fa-check`
    - SVG path data: `M5 13l4 4L19 7`

14. **users_icon.svg** - Used for empty state in friends listing
    - Font Awesome icon: `fa-users`
    - SVG path data: `M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z`

15. **edit_icon.svg** - Used for edit friend button
    - Font Awesome icon: `fa-edit`
    - SVG path data: `M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z`

16. **chat_icon.svg** - Used for start chat button
    - Font Awesome icon: `fa-comment`
    - SVG path data: `M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z`

### ProfileFormComponent.xaml
(Uses icons already defined above: arrow_left_icon.svg, error_icon.svg, check_icon.svg)

## Implementation Notes

### SVG File Format
Each SVG file should be a standard SVG format with the following structure:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <path d="[PATH_DATA_HERE]"/>
</svg>
```

### Color Handling
- SVG files should use `currentColor` or `stroke="currentColor"` to allow dynamic color changes from XAML
- The XAML implementation will handle color changes through the `Color` property of Image elements

### Size Considerations
- All icons are designed for 24x24 viewBox but will scale appropriately
- XAML implementation uses WidthRequest and HeightRequest to control display size

### Alternative Sources
If Font Awesome icons are not available, these icons can be sourced from:
- Heroicons (https://heroicons.com/)
- Feather Icons (https://feathericons.com/)
- Lucide Icons (https://lucide.dev/)

## Summary of Changes

### Components Updated
1. **FakeCaptchaScreen.xaml** - Enhanced loading states, improved captcha controls, removed custom focus
2. **SignInFormComponent.xaml** - Added header icon, improved password toggle, removed custom focus
3. **FriendProfileFormComponent.xaml** - Replaced font icons with SVG assets, removed custom focus
4. **FriendsListingComponent.xaml** - Complete redesign with search, error handling, empty states
5. **ProfileFormComponent.xaml** - Replaced font icons with SVG assets, removed custom focus

## Migration Benefits

### Removed Dependencies
- No longer dependent on FluentSystemIcons font
- No longer using inline SVG path data in XAML
- Removed custom focus highlighting converters and logic
- Eliminated ~100+ lines of custom focus management code across all components

### Improved Maintainability
- Icons are now external assets that can be easily updated
- Consistent icon styling across the application
- Platform-native focus behavior for better accessibility
- Unified approach to error handling and success messages

### Performance Benefits
- Reduced XAML file size by removing inline SVG paths
- Better caching of icon assets
- Simplified binding logic without focus state management
- Improved memory usage by removing unnecessary property change notifications
