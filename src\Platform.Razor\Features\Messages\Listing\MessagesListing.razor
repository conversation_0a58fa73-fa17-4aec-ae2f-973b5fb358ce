@page "/chats-new"

<!-- Messages/Chat Threads Listing - WhatsApp Style -->
<div class="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Compact Header -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">Messages</h1>
            <div class="flex items-center space-x-3">
                <!-- Search Button -->
                <button @onclick="ToggleSearch" 
                        class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
                <!-- New Chat Button -->
                <button @onclick="StartNewChat" 
                        class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Search Bar (Collapsible) -->
        @if (showSearch)
        {
            <div class="mt-3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" 
                           @bind="searchQuery" 
                           @bind:event="oninput"
                           placeholder="Search conversations..." 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                </div>
            </div>
        }
    </div>

    <!-- Chat Threads List -->
    <div class="flex-1 overflow-y-auto">
        @if (isLoading)
        {
            <!-- Loading State -->
            <div class="flex items-center justify-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        }
        else if (filteredChats.Count == 0)
        {
            <!-- Empty State -->
            <div class="flex flex-col items-center justify-center py-12 px-4">
                <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No conversations yet</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 text-center mb-4">
                    Start a new conversation with your friends
                </p>
                <button @onclick="StartNewChat" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                    Start New Chat
                </button>
            </div>
        }
        else
        {
            <!-- Chat Threads -->
            <div class="divide-y divide-gray-200 dark:divide-gray-700">
                @foreach (var chat in filteredChats)
                {
                    <div @onclick="() => OpenChat(chat.Id)" 
                         class="flex items-center px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200">
                        <!-- Avatar -->
                        <div class="flex-shrink-0 mr-3">
                            @if (!string.IsNullOrEmpty(chat.AvatarUrl))
                            {
                                <img src="@chat.AvatarUrl" alt="@chat.Name" 
                                     class="w-12 h-12 rounded-full object-cover" />
                            }
                            else
                            {
                                <div class="w-12 h-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                    <span class="text-lg font-medium text-gray-700 dark:text-gray-300">
                                        @GetInitials(chat.Name)
                                    </span>
                                </div>
                            }
                        </div>
                        
                        <!-- Chat Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    @chat.Name
                                </h3>
                                <span class="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2">
                                    @GetFormattedTime(chat.LastMessageTime)
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                                    @if (chat.IsTyping)
                                    {
                                        <span class="text-blue-600 dark:text-blue-400 italic">typing...</span>
                                    }
                                    else
                                    {
                                        @chat.LastMessage
                                    }
                                </p>
                                @if (chat.UnreadCount > 0)
                                {
                                    <div class="bg-blue-600 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center px-2 ml-2">
                                        @(chat.UnreadCount > 99 ? "99+" : chat.UnreadCount.ToString())
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>
