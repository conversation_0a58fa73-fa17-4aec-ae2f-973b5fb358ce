using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Friends;
using DeepMessage.ServiceContracts;

namespace Platform.Client.Services.Features.Friends;

public class FriendProfileClientSideFormDataService : IFriendProfileFormDataService
{
    private readonly BaseHttpClient _httpClient;

    public FriendProfileClientSideFormDataService(BaseHttpClient context)
    {
        _httpClient = context;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(FriendProfileFormBusinessObject formBusinessObject)
    {
        return await _httpClient.PostAsJsonAsync<string>($"api/FriendProfilesForm/Save", formBusinessObject);
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<FriendProfileFormBusinessObject?> GetItemByIdAsync(string id)
    {
        return await _httpClient.GetFromJsonAsync<FriendProfileFormBusinessObject>($"api/FriendProfilesForm/GetItemById?id=" + id);
    }
}
