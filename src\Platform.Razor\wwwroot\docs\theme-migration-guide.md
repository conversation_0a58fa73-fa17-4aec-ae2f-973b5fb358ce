# Theme System Migration Guide

This guide explains the architectural improvements made to the DeepMessage theme system and how to use the new integrated Tailwind + CSS Variables approach.

## What Changed

### Before (Custom Utility Classes)
```razor
<div class="dm-header dm-shadow-sm">
    <h1 class="dm-text-primary">Title</h1>
    <button class="dm-text-secondary hover:dm-text-primary dm-transition-colors">
        Action
    </button>
</div>
```

### After (Integrated Tailwind Classes)
```razor
<div class="bg-header shadow-theme-sm">
    <h1 class="text-primary">Title</h1>
    <button class="text-secondary hover:text-primary transition-theme">
        Action
    </button>
</div>
```

## New Color System

### Semantic Color Classes
| Old Class | New Class | Purpose |
|-----------|-----------|---------|
| `dm-text-primary` | `text-primary` | Primary text color |
| `dm-text-secondary` | `text-secondary` | Secondary text color |
| `dm-text-tertiary` | `text-tertiary` | Tertiary text color |
| `dm-text-accent` | `text-accent` | Accent color (red) |
| `dm-bg-primary` | `bg-background` | Main background |
| `dm-bg-secondary` | `bg-muted` | Secondary background |
| `dm-bg-surface` | `bg-surface` | Surface background |
| `dm-border-primary` | `border-border` | Primary border |

### Component-Specific Colors
| Old Class | New Class | Purpose |
|-----------|-----------|---------|
| `dm-header` | `bg-header` | Header backgrounds |
| `dm-nav` | `bg-nav` | Navigation backgrounds |
| `dm-chat-container` | `bg-chat` | Chat container |
| `dm-dialog` | `bg-dialog` | Dialog backgrounds |

## Utility Class Updates

### Shadows
| Old Class | New Class |
|-----------|-----------|
| `dm-shadow-sm` | `shadow-theme-sm` |
| `dm-shadow-md` | `shadow-theme-md` |
| `dm-shadow-lg` | `shadow-theme-lg` |

### Transitions
| Old Class | New Class |
|-----------|-----------|
| `dm-transition-colors` | `transition-theme` |
| `dm-transition` | `transition-theme-all` |

### Focus States
| Old Class | New Class |
|-----------|-----------|
| `dm-focus` | `focus-theme` |

## Migration Examples

### Header Component
```razor
<!-- Before -->
<header class="dm-header border-b dm-border-primary px-4 py-3 dm-shadow-sm">
    <h1 class="dm-text-primary">Chat</h1>
</header>

<!-- After -->
<header class="bg-header border-b border-border px-4 py-3 shadow-theme-sm">
    <h1 class="text-primary">Chat</h1>
</header>
```

### Navigation Component
```razor
<!-- Before -->
<nav class="dm-nav border-t dm-border-primary">
    <button class="dm-text-secondary hover:dm-text-primary dm-transition-colors">
        Home
    </button>
</nav>

<!-- After -->
<nav class="bg-nav border-t border-border">
    <button class="text-secondary hover:text-primary transition-theme">
        Home
    </button>
</nav>
```

### Form Input
```razor
<!-- Before -->
<input class="dm-bg-surface border dm-border-primary dm-text-primary dm-focus" />

<!-- After -->
<input class="bg-surface border border-border text-primary focus-theme" />
```

### Chat Bubbles
```razor
<!-- Before -->
<div class="dm-chat-bubble-sent">
    <p class="dm-text-primary">Message</p>
</div>

<!-- After -->
<div class="bg-bubble-sent">
    <p class="text-primary">Message</p>
</div>
```

## Benefits of New System

### 1. Single Learning Curve
- Only need to learn Tailwind classes
- No custom utility classes to remember
- Better IntelliSense support

### 2. Smaller Bundle Size
- Eliminated duplicate CSS utilities
- Better tree-shaking
- Reduced maintenance overhead

### 3. Better Developer Experience
- Consistent patterns across components
- Standard Tailwind naming conventions
- Improved tooling support

### 4. Runtime Theme Switching
- CSS variables still enable instant theme switching
- No JavaScript required for theme changes
- Smooth transitions between light/dark modes

## CSS Variables (Unchanged)

The underlying CSS variables remain the same and continue to power the theme system:

```css
:root {
  --dm-text-primary: var(--dm-gray-900);
  --dm-bg-surface: var(--dm-white);
  --dm-border-primary: var(--dm-gray-200);
  /* ... */
}

.dark {
  --dm-text-primary: var(--dm-white);
  --dm-bg-surface: var(--dm-gray-900);
  --dm-border-primary: var(--dm-gray-700);
  /* ... */
}
```

## Component Patterns

Use the documented patterns from `component-patterns.md` for consistent styling:

### Header Pattern
```razor
<header class="bg-header border-b border-border px-4 py-3 shadow-theme-sm">
    <!-- header content -->
</header>
```

### Button Pattern
```razor
<button class="text-secondary hover:text-primary hover:bg-muted transition-theme focus-theme">
    <!-- button content -->
</button>
```

### Input Pattern
```razor
<input class="bg-surface border border-border text-primary placeholder:text-tertiary focus-theme transition-theme" />
```

## Dark Mode Support

Dark mode continues to work automatically through CSS variables:

```html
<!-- Light mode (default) -->
<html>

<!-- Dark mode -->
<html class="dark">
```

All components automatically switch themes without any code changes.

## Troubleshooting

### Common Issues

1. **Missing Colors**: Ensure you're using the new color names (`text-primary` not `dm-text-primary`)
2. **Broken Styles**: Check that Tailwind config includes the CSS variables
3. **No IntelliSense**: Restart your editor after the Tailwind config changes

### Verification

Test that your component works in both light and dark modes:

```javascript
// Toggle dark mode for testing
document.documentElement.classList.toggle('dark');
```

## Next Steps

1. Update any remaining components using the old classes
2. Remove unused CSS from theme-system.css
3. Update component documentation
4. Test theme switching functionality

The new system provides a more maintainable, performant, and developer-friendly approach while preserving all existing functionality.
