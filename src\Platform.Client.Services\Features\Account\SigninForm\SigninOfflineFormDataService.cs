using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Text.Json;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;

namespace Platform.Client.Services.Features.Account.SigninForm
{
    public class SignInOfflineFormDataService : ISignInFormDataService
    {
        //private readonly IClientEncryptionService _encryptionService;
        private readonly ILocalStorageService _localStorageService;
        private readonly ISecureKeyManager _secureKeyManager;
        private readonly AppDbContext context;

        public SignInOfflineFormDataService(ILocalStorageService localStorageService,
            ISecureKeyManager secureKeyManager, AppDbContext context)
        {
            _localStorageService = localStorageService;
            _secureKeyManager = secureKeyManager;
            this.context = context;
        }

        [SystemClaim(SystemClaimType.SystemDefault)]
        public async Task<string> SaveAsync(SignInFormBusinessObject formBusinessObject)
        {
            try
            {
                // Step 1: Check if user data exists in local storage
                var storedUsername = await _localStorageService.GetValue(ClaimTypes.Name);
                var storedUserId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);

                if (string.IsNullOrEmpty(storedUsername) || string.IsNullOrEmpty(storedUserId))
                {
                    return "Authenticate Online";
                }
                // Step 2: Validate provided username matches stored username
                if (!string.Equals(formBusinessObject.NickName, storedUsername, StringComparison.OrdinalIgnoreCase))
                {
                    return "Authenticate Online";
                }

                // Step 3: Check if encrypted private key exists for this user
                var encryptedPrivateKey = await _localStorageService.GetValue("pub2e_");
                if (string.IsNullOrEmpty(encryptedPrivateKey))
                {
                    return "Authenticate Online";
                }

                var user = await context.ApplicationUsers.FirstOrDefaultAsync(x => x.Id == storedUserId);
                if (user == null)
                {
                    return "Authenticate Online";
                }

                // Step 4: Attempt to derive keys and authenticate using SecureKeyManager
                var authenticationSuccess = await _secureKeyManager.DeriveAndStoreKeysAsync(
                    formBusinessObject.NickName!,
                    formBusinessObject.PassKey!);

                if (!authenticationSuccess)
                {
                    throw new Exception("Invalid credentials");
                }

                // Step 5: Verify RSA key is available in memory
                if (!_secureKeyManager.IsRSAKeyAvailable())
                {
                    throw new Exception("Failed to load cryptographic keys");
                }

                // Step 6: Authentication successful - create AuthorizationClaimsModel
                // Retrieve stored public keys for the user
                var storedPub1 = await _localStorageService.GetValue("pub1o_") ?? string.Empty;
                var storedPub2 = await _localStorageService.GetValue("pub2e_") ?? string.Empty;

                var authClaims = new AuthorizationClaimsModel(
                    token: string.Empty,
                    refreshToken: string.Empty, // No refresh token for offline auth
                    userId: storedUserId,
                    username: storedUsername,
                    pub1: storedPub1, // User's public key
                    pub2: storedPub2,  // User's encrypted private key
                    displayName: user.NickName,
                    avatarData: user.AvatarData,
                    avatarDescription: user.AvatarDescription
                );

                return JsonSerializer.Serialize(authClaims);
            }
            catch (Exception ex)
            {
                // Clear keys on authentication failure
                _secureKeyManager.ClearKeys();
                throw new Exception($"Offline authentication failed: {ex.Message}");
            }
        }

        [SystemClaim(SystemClaimType.SystemDefault)]
        public Task<SignInFormBusinessObject> GetItemByIdAsync(string id)
        {
            throw new NotImplementedException("GetItemByIdAsync not supported for offline authentication");
        }
    }
}
