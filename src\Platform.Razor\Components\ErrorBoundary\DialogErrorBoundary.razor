@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Components.Web
@inject ILogger<DialogErrorBoundary> Logger

<!-- Dialog-Specific Error Boundary -->
<ErrorBoundary @ref="errorBoundary">
    <ChildContent>
        @ChildContent
    </ChildContent>
    <ErrorContent Context="exception">
        <div class="p-6 text-center">
            <!-- Error Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
                <svg class="h-8 w-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <!-- Error Title -->
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Dialog Error
            </h3>

            <!-- Error Message -->
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                @if (IsProduction)
                {
                    <p>Unable to load this dialog. Please try again or close and reopen.</p>
                }
                else
                {
                    <div class="text-left bg-red-50 dark:bg-red-900/20 rounded-lg p-3 mb-4">
                        <p class="font-medium text-red-800 dark:text-red-200 mb-1">@exception.GetType().Name</p>
                        <p class="text-red-700 dark:text-red-300 text-xs">@exception.Message</p>
                    </div>
                }
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button @onclick="RetryDialog" 
                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Try Again
                </button>
                
                <button @onclick="CloseDialog" 
                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Close Dialog
                </button>
            </div>

            <!-- Error ID for Support -->
            @if (!IsProduction)
            {
                <div class="mt-4 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded p-2">
                    Error ID: @ErrorId
                </div>
            }
        </div>
    </ErrorContent>
</ErrorBoundary>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public string DialogTitle { get; set; } = "Dialog";
    [Parameter] public EventCallback OnRetry { get; set; }
    [Parameter] public EventCallback OnClose { get; set; }

    private ErrorBoundary? errorBoundary;
    private string ErrorId = Guid.NewGuid().ToString("N")[..8];
    private bool IsProduction => Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != "Development";

    protected override void OnParametersSet()
    {
        // Reset error boundary when parameters change
        errorBoundary?.Recover();
    }

    private async Task RetryDialog()
    {
        try
        {
            Logger.LogInformation("Retrying dialog: {DialogTitle}, Error ID: {ErrorId}", DialogTitle, ErrorId);
            
            // Generate new error ID for retry
            ErrorId = Guid.NewGuid().ToString("N")[..8];
            
            errorBoundary?.Recover();
            
            if (OnRetry.HasDelegate)
            {
                await OnRetry.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during dialog retry: {DialogTitle}", DialogTitle);
        }
    }

    private async Task CloseDialog()
    {
        try
        {
            Logger.LogInformation("Closing dialog due to error: {DialogTitle}, Error ID: {ErrorId}", DialogTitle, ErrorId);
            
            if (OnClose.HasDelegate)
            {
                await OnClose.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during dialog close: {DialogTitle}", DialogTitle);
        }
    }

    public void HandleException(Exception exception)
    {
        ErrorId = Guid.NewGuid().ToString("N")[..8];
        
        Logger.LogError(exception, 
            "Dialog error in {DialogTitle}. Error ID: {ErrorId}. Exception: {ExceptionType} - {Message}", 
            DialogTitle, ErrorId, exception.GetType().Name, exception.Message);
    }
}
