<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Nothing Phone Gradients with Animation Support -->
    <linearGradient id="animatedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1">
        <animate attributeName="stop-opacity" values="1;0.8;1" dur="8s" repeatCount="indefinite"/>
      </stop>
      <stop offset="50%" style="stop-color:#fafafa;stop-opacity:0.9">
        <animate attributeName="stop-opacity" values="0.9;0.7;0.9" dur="6s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" style="stop-color:#f5f5f5;stop-opacity:0.8">
        <animate attributeName="stop-opacity" values="0.8;0.6;0.8" dur="10s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>
    
    <radialGradient id="pulseGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#eeeeee;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:0.2"/>
      <animateTransform attributeName="gradientTransform" type="scale" 
                        values="1;1.1;1" dur="12s" repeatCount="indefinite"/>
    </radialGradient>
    
    <!-- Floating Particle Pattern -->
    <pattern id="floatingDots" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <circle cx="50" cy="50" r="1" fill="#bdbdbd" opacity="0.4">
        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite"/>
        <animateTransform attributeName="transform" type="translate" 
                          values="0,0; 2,2; 0,0" dur="6s" repeatCount="indefinite"/>
      </circle>
    </pattern>
  </defs>
  
  <!-- Animated Base Background -->
  <rect width="400" height="600" fill="url(#animatedGradient)"/>
  
  <!-- Gently Moving Shapes -->
  <circle cx="120" cy="180" r="60" fill="url(#pulseGradient)" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 10,5; 0,0" dur="15s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="300" cy="400" r="80" fill="url(#pulseGradient)" opacity="0.4">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; -8,8; 0,0" dur="18s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="80" cy="480" r="45" fill="url(#pulseGradient)" opacity="0.3">
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 5,-5; 0,0" dur="12s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Flowing Lines with Gentle Animation -->
  <path d="M0,200 Q200,180 400,200" stroke="#e0e0e0" stroke-width="1" fill="none" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.8;0.5" dur="8s" repeatCount="indefinite"/>
    <animate attributeName="d" values="M0,200 Q200,180 400,200;M0,200 Q200,190 400,200;M0,200 Q200,180 400,200" 
             dur="10s" repeatCount="indefinite"/>
  </path>
  
  <path d="M0,350 Q150,330 400,350" stroke="#bdbdbd" stroke-width="0.8" fill="none" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.7;0.4" dur="12s" repeatCount="indefinite"/>
    <animate attributeName="d" values="M0,350 Q150,330 400,350;M0,350 Q150,340 400,350;M0,350 Q150,330 400,350" 
             dur="14s" repeatCount="indefinite"/>
  </path>
  
  <!-- Subtle Message Bubble Hints with Breathing Effect -->
  <ellipse cx="280" cy="160" rx="35" ry="18" fill="#f5f5f5" opacity="0.6">
    <animateTransform attributeName="transform" type="scale" 
                      values="1;1.05;1" dur="6s" repeatCount="indefinite"/>
  </ellipse>
  
  <ellipse cx="130" cy="380" rx="30" ry="15" fill="#eeeeee" opacity="0.5">
    <animateTransform attributeName="transform" type="scale" 
                      values="1;1.08;1" dur="8s" repeatCount="indefinite"/>
  </ellipse>
  
  <!-- Floating Particles -->
  <rect width="400" height="600" fill="url(#floatingDots)" opacity="0.6"/>
  
  <!-- Gentle Accent Pulses -->
  <circle cx="350" cy="100" r="8" fill="#dc2626" opacity="0.1">
    <animate attributeName="opacity" values="0.1;0.2;0.1" dur="5s" repeatCount="indefinite"/>
    <animateTransform attributeName="transform" type="scale" 
                      values="1;1.2;1" dur="5s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="70" cy="300" r="6" fill="#dc2626" opacity="0.08">
    <animate attributeName="opacity" values="0.08;0.15;0.08" dur="7s" repeatCount="indefinite"/>
    <animateTransform attributeName="transform" type="scale" 
                      values="1;1.3;1" dur="7s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Nothing Phone Corner Elements with Subtle Fade -->
  <rect x="10" y="10" width="1.5" height="25" fill="#9e9e9e" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.6;0.4" dur="10s" repeatCount="indefinite"/>
  </rect>
  
  <rect x="388.5" y="565" width="1.5" height="25" fill="#9e9e9e" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.6;0.4" dur="12s" repeatCount="indefinite"/>
  </rect>
</svg>
