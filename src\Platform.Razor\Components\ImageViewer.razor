@using DeepMessage.ServiceContracts.Features.Conversation
@inject IJSRuntime JSRuntime

@* Full-Screen Image Viewer Modal Component *@
@if (IsVisible)
{
    <div class="image-viewer-overlay" @onclick="CloseViewer" @onkeydown="HandleKeyDown" tabindex="0">
        <div class="image-viewer-container" @onclick:stopPropagation="true">
            <!-- Header with close button and image info -->
            <div class="image-viewer-header">
                <div class="image-info">
                    <h3 class="image-title">@CurrentAttachment?.FileName</h3>
                    @if (CurrentAttachment?.FileSizeBytes.HasValue == true)
                    {
                        <span class="image-size">@FormatFileSize(CurrentAttachment.FileSizeBytes.Value)</span>
                    }
                </div>
                <button class="close-button" @onclick="CloseViewer" aria-label="Close">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Main image display area -->
            <div class="image-display-area" @onwheel="HandleWheel" @onmousedown="HandleMouseDown" @onmousemove="HandleMouseMove" @onmouseup="HandleMouseUp" @onmouseleave="HandleMouseLeave">
                @if (CurrentAttachment != null)
                {
                    @if (IsLoading)
                    {
                        <div class="loading-container">
                            <div class="loading-spinner"></div>
                            <span class="loading-text">Loading image...</span>
                        </div>
                    }
                    else
                    {
                        <img @ref="imageElement"
                             src="@GetImageSource(CurrentAttachment)"
                             alt="@CurrentAttachment.FileName"
                             class="viewer-image"
                             style="transform: scale(@ZoomLevel) translate(@PanX px, @PanY px)"
                             @onload="OnImageLoaded"
                             @onerror="OnImageError"
                             draggable="false" />
                    }
                }
            </div>

            <!-- Navigation controls (if multiple images) -->
            @if (Attachments?.Count > 1)
            {
                <div class="navigation-controls">
                    <button class="nav-button prev-button" @onclick="ShowPreviousImage" disabled="@(CurrentIndex <= 0)">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    
                    <span class="image-counter">@(CurrentIndex + 1) of @Attachments.Count</span>
                    
                    <button class="nav-button next-button" @onclick="ShowNextImage" disabled="@(CurrentIndex >= Attachments.Count - 1)">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            }

            <!-- Zoom controls -->
            <div class="zoom-controls">
                <button class="zoom-button" @onclick="ZoomOut" disabled="@(ZoomLevel <= MinZoom)">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                    </svg>
                </button>
                
                <span class="zoom-level">@((int)(ZoomLevel * 100))%</span>
                
                <button class="zoom-button" @onclick="ZoomIn" disabled="@(ZoomLevel >= MaxZoom)">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                </button>
                
                <button class="zoom-button" @onclick="ResetZoom">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
}

<style>
    .image-viewer-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(4px);
    }

    .image-viewer-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        max-width: 100vw;
        max-height: 100vh;
    }

    .image-viewer-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 10;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
        color: white;
    }

    .image-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .image-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin: 0;
    }

    .image-size {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    .close-button {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .close-button:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .image-display-area {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        cursor: grab;
        user-select: none;
    }

    .image-display-area.dragging {
        cursor: grabbing;
    }

    .viewer-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        transition: transform 0.2s ease;
        pointer-events: none;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: white;
    }

    .loading-spinner {
        width: 3rem;
        height: 3rem;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top-color: white;
        border-radius: 50%;
    }

    .loading-text {
        font-size: 1rem;
        opacity: 0.8;
    }

    .navigation-controls {
        position: absolute;
        bottom: 1rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        gap: 1rem;
        background: rgba(0, 0, 0, 0.7);
        padding: 0.75rem 1.5rem;
        border-radius: 2rem;
        color: white;
    }

    .nav-button {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .nav-button:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.2);
    }

    .nav-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .image-counter {
        font-size: 0.875rem;
        font-weight: 500;
        min-width: 4rem;
        text-align: center;
    }

    .zoom-controls {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(0, 0, 0, 0.7);
        padding: 0.5rem;
        border-radius: 1.5rem;
        color: white;
    }

    .zoom-button {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .zoom-button:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.2);
    }

    .zoom-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .zoom-level {
        font-size: 0.75rem;
        font-weight: 500;
        min-width: 3rem;
        text-align: center;
    }

    /* Mobile responsive adjustments - simplified for Razor compatibility */
</style>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public List<MessageAttachmentInfo>? Attachments { get; set; }
    [Parameter] public int InitialIndex { get; set; } = 0;
    [Parameter] public EventCallback OnClose { get; set; }

    private ElementReference imageElement;
    private bool IsLoading { get; set; } = true;
    private int CurrentIndex { get; set; } = 0;
    private double ZoomLevel { get; set; } = 1.0;
    private double PanX { get; set; } = 0;
    private double PanY { get; set; } = 0;
    private bool IsDragging { get; set; } = false;
    private double LastMouseX { get; set; }
    private double LastMouseY { get; set; }

    private const double MinZoom = 0.5;
    private const double MaxZoom = 5.0;
    private const double ZoomStep = 0.25;

    private MessageAttachmentInfo? CurrentAttachment => 
        Attachments?.ElementAtOrDefault(CurrentIndex);

    protected override void OnParametersSet()
    {
        if (IsVisible && Attachments?.Any() == true)
        {
            CurrentIndex = Math.Max(0, Math.Min(InitialIndex, Attachments.Count - 1));
            ResetZoom();
        }
    }

    private async Task CloseViewer()
    {
        await OnClose.InvokeAsync();
    }

    private void ShowPreviousImage()
    {
        if (CurrentIndex > 0)
        {
            CurrentIndex--;
            ResetZoom();
            IsLoading = true;
            StateHasChanged();
        }
    }

    private void ShowNextImage()
    {
        if (Attachments != null && CurrentIndex < Attachments.Count - 1)
        {
            CurrentIndex++;
            ResetZoom();
            IsLoading = true;
            StateHasChanged();
        }
    }

    private void ZoomIn()
    {
        ZoomLevel = Math.Min(MaxZoom, ZoomLevel + ZoomStep);
    }

    private void ZoomOut()
    {
        ZoomLevel = Math.Max(MinZoom, ZoomLevel - ZoomStep);
        
        // Reset pan if zoomed out too much
        if (ZoomLevel <= 1.0)
        {
            PanX = 0;
            PanY = 0;
        }
    }

    private void ResetZoom()
    {
        ZoomLevel = 1.0;
        PanX = 0;
        PanY = 0;
    }

    private void OnImageLoaded()
    {
        IsLoading = false;
        StateHasChanged();
    }

    private void OnImageError()
    {
        IsLoading = false;
        StateHasChanged();
    }

    private string GetImageSource(MessageAttachmentInfo attachment)
    {
        if (!string.IsNullOrEmpty(attachment.ThumbnailBase64))
        {
            return $"data:image/jpeg;base64,{attachment.ThumbnailBase64}";
        }
        else if (!string.IsNullOrEmpty(attachment.FileUrl))
        {
            return attachment.FileUrl;
        }
        return "";
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.#} {sizes[order]}";
    }

    // Event handlers for zoom and pan
    private async Task HandleKeyDown(KeyboardEventArgs e)
    {
        switch (e.Key)
        {
            case "Escape":
                await CloseViewer();
                break;
            case "ArrowLeft":
                ShowPreviousImage();
                break;
            case "ArrowRight":
                ShowNextImage();
                break;
            case "+":
            case "=":
                ZoomIn();
                break;
            case "-":
                ZoomOut();
                break;
            case "0":
                ResetZoom();
                break;
        }
    }

    private void HandleWheel(WheelEventArgs e)
    {
        if (e.DeltaY < 0)
        {
            ZoomIn();
        }
        else
        {
            ZoomOut();
        }
    }

    private void HandleMouseDown(MouseEventArgs e)
    {
        if (ZoomLevel > 1.0)
        {
            IsDragging = true;
            LastMouseX = e.ClientX;
            LastMouseY = e.ClientY;
        }
    }

    private void HandleMouseMove(MouseEventArgs e)
    {
        if (IsDragging && ZoomLevel > 1.0)
        {
            var deltaX = e.ClientX - LastMouseX;
            var deltaY = e.ClientY - LastMouseY;
            
            PanX += deltaX / ZoomLevel;
            PanY += deltaY / ZoomLevel;
            
            LastMouseX = e.ClientX;
            LastMouseY = e.ClientY;
        }
    }

    private void HandleMouseUp(MouseEventArgs e)
    {
        IsDragging = false;
    }

    private void HandleMouseLeave(MouseEventArgs e)
    {
        IsDragging = false;
    }
}
