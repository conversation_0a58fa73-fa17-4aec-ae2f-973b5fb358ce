using System.Globalization;

namespace MobileApp.MauiShared.Converters
{
    /// <summary>
    /// Converter to handle base64 image data for MAUI Image controls
    /// </summary>
    public class Base64ToImageConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
                return null;

            var base64String = value.ToString();
            if (string.IsNullOrEmpty(base64String))
                return null;

            try
            {
                // Handle data URL format (data:image/png;base64,...)
                if (base64String.StartsWith("data:"))
                {
                    var commaIndex = base64String.IndexOf(',');
                    if (commaIndex >= 0 && commaIndex < base64String.Length - 1)
                    {
                        base64String = base64String.Substring(commaIndex + 1);
                    }
                }

                // Convert base64 to byte array
                var imageBytes = System.Convert.FromBase64String(base64String);
                
                // Create ImageSource from byte array
                return ImageSource.FromStream(() => new MemoryStream(imageBytes));
            }
            catch (Exception)
            {
                // Return null if conversion fails - fallback to initials will be used
                return null;
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
