﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Platform.Client.Common.Features.AuthCodes;
using Platform.Client.Services.Features.AuthCodes;
using System.Windows.Input;
namespace Platform.Client.Common.Features.AuthCodes;
public class AuthCodeListingViewBase : ListingBaseMaui<AuthCodeListingViewModel, AuthCodeListingBusinessObject,
                AuthCodeFilterViewModel, AuthCodeFilterBusinessObject, IAuthCodeListingDataService>
{
    private ICommand? _syncDownItemsCommand;
    public ICommand? SyncDownItemsCommand
    {
        get
        {
            return _syncDownItemsCommand = _syncDownItemsCommand ?? new Command(() =>
            {
                Navigation.PushModalAsync(new AuthCodeFormView(ScopeFactory, string.Empty));
            });
        }
    }

    public AuthCodeListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class AuthCodeListingView : AuthCodeListingViewBase
{
    public AuthCodeListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent();
        BindingContext = this;
    }
}


