﻿using System.Collections.ObjectModel;
using System.ComponentModel;

namespace Platform.Framework.Core
{
    public class DialogService
    {
        public ObservableCollection<ModalDialogConfig> Dialogs { get; set; }

        public DialogService()
        {
            Dialogs = new ObservableCollection<ModalDialogConfig>();
        }


        public void ShowDialogAsync(ModalDialogConfig dialogConfig)
        {
            Dialogs.Add(dialogConfig);
        }


    }
    public class ModalDialogConfig
    {

        public ModalDialogConfig()
        {
            Id = $"Dialog{DateTime.Now.ToString("HHMMssff")}_";
            Parameters = new Dictionary<string, object>();
        }

        public void AddParameter(string key, object value)
        {
            Parameters.Add(key, value);
        }

        public string Id { get; set; }

        public string Title { get; set; }

        public string SizeClasses { get; set; }

        public string PositionClasses { get; set; }

        public string DialogContainerClasses { get; set; }

        public bool ShowCrossIcon { get; set; } = true;

        public Type Component { get; set; }

        public IDictionary<string, object> Parameters { get; set; }
    }

    public class KtNotificationService
    {
        public ObservableCollection<Notification> Notifications { get; set; }

        public KtNotificationService()
        {
            Notifications = new ObservableCollection<Notification>();
        }



        public void ShowError(Exception ex)
        {
            string message = ex.Message;
            if (ex is HttpRequestException || ex is TaskCanceledException || ex.Message.Contains("The request was canceled due to the configured HttpClient"))
            {
                message = "Please check your internet and try again";
            } 

            if (Notifications.Any(x => x.Message == message))
                return;

            Notifications.Add(new Notification()
            {
                Message = message,
                NotificationType = NotificationType.Error,
            });
        }

        public void ShowError(string message)
        { 
            if (Notifications.Any(x => x.Message == message))
                return;

            Notifications.Add(new Notification()
            {
                Message = message,
                NotificationType = NotificationType.Error,
            });
        }
    }

    public class AlertService
    {
        public event Action<string, MessageTypes> OnShowAlert;

        public void Show(string message, MessageTypes type)
        {
            OnShowAlert?.Invoke(message, type);
        }

    }

    public class Notification
    {
        public string? Message { get; set; }
        public NotificationType NotificationType { get; set; }
    }

    public enum NotificationType
    {
        Info,
        Warning,
        Error,

    }

    public enum MessageTypes : short
    {
        [Description("success")]
        Success = 1,
        [Description("warning")]
        Warning = 2,
        [Description("info")]
        Info = 3,
        [Description("danger")]
        Error = 4
    }
}

