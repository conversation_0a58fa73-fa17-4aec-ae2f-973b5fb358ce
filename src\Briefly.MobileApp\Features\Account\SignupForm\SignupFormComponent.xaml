﻿<?xml version="1.0" encoding="utf-8" ?>
<local:SignupFormViewBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.SignupFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="Create your account"
    x:DataType="local:SignupFormView"
    BackgroundColor="#004f98"
    IsBusy="{Binding IsWorking}"
    Shell.BackgroundColor="#004f98"
    Shell.TitleColor="White">
    
    <Border BackgroundColor="#F4F4F5" StrokeThickness="0">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="16,16,0,0" />
        </Border.StrokeShape>
        
        <ScrollView>
            <VerticalStackLayout Padding="16" Spacing="20">
                
                <!-- Header Section -->
                <VerticalStackLayout Spacing="8" Margin="0,20,0,10">
                    <Label
                        FontAttributes="Bold"
                        FontFamily="MulishExtraBold"
                        FontSize="24"
                        HorizontalTextAlignment="Center"
                        Text="Create your account"
                        TextColor="#004f98" />
                    <Label
                        FontFamily="Poppins"
                        FontSize="14"
                        HorizontalTextAlignment="Center"
                        Text="Join us to get started with secure messaging"
                        TextColor="#6B7280"
                        Opacity="0.8" />
                </VerticalStackLayout>

                <!-- Error Display -->
                <Border IsVisible="{Binding HasError}" BackgroundColor="#FEF2F2" StrokeThickness="1" Stroke="#FECACA">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Path Grid.Column="0" 
                              Fill="#DC2626" 
                              VerticalOptions="Start"
                              Margin="0,2,8,0"
                              Data="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        <VerticalStackLayout Grid.Column="1" Spacing="4">
                            <Label FontAttributes="Bold" FontSize="14" Text="Registration failed" TextColor="#DC2626" />
                            <Label FontSize="12" Text="{Binding Error}" TextColor="#991B1B" />
                        </VerticalStackLayout>
                    </Grid>
                </Border>

                <!-- Sign Up Form -->
                <VerticalStackLayout Spacing="16">
                    
                    <!-- Avatar Selection -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="14"
                            Text="Select Avatar"
                            TextColor="#4E4E4E" />
                        
                        <!-- Avatar Preview -->
                        <HorizontalStackLayout HorizontalOptions="Center" Spacing="16" Margin="0,8">
                            <Border
                                BackgroundColor="LightGray"
                                WidthRequest="80"
                                HeightRequest="80"
                                StrokeThickness="2"
                                Stroke="#004f98">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="40" />
                                </Border.StrokeShape>
                                <Image Source="{Binding SelectedItem.AvatarData}"
                                       IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                       Aspect="AspectFill" />
                                <Label IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                       Text="{Binding SelectedItem.DisplayName, Converter={StaticResource InitialsConverter}}"
                                       FontSize="24"
                                       FontAttributes="Bold"
                                       TextColor="#004f98"
                                       HorizontalOptions="Center"
                                       VerticalOptions="Center" />
                            </Border>
                        </HorizontalStackLayout>
                        
                        <Border
                            Background="#EEF3F4"
                            HeightRequest="48"
                            StrokeThickness="1"
                            Stroke="#E5E7EB">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="12" />
                            </Border.StrokeShape>
                            <Picker
                                ItemsSource="{Binding AvatarOptions}"
                                SelectedItem="{Binding SelectedItem.AvatarData}"
                                Title="Choose an avatar..."
                                FontSize="14"
                                TextColor="#4E4E4E"
                                TitleColor="#9CA3AF"
                                Margin="16,0" />
                        </Border>
                    </VerticalStackLayout>

                    <!-- Display Name Field -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="14"
                            Text="Display Name"
                            TextColor="#4E4E4E" />
                        <Border
                            Background="#EEF3F4"
                            HeightRequest="56"
                            StrokeThickness="1"
                            Stroke="{Binding DisplayNameFieldFocused, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#004f98|#E5E7EB'}">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="12" />
                            </Border.StrokeShape>
                            <Grid Margin="16,0" ColumnDefinitions="Auto,*">
                                <Path Grid.Column="0" 
                                      Fill="#6B7280" 
                                      VerticalOptions="Center"
                                      Margin="0,0,12,0"
                                      Data="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                <Entry Grid.Column="1"
                                       FontSize="16"
                                       Placeholder="Your friends will see this name"
                                       PlaceholderColor="#9CA3AF"
                                       Text="{Binding SelectedItem.DisplayName}"
                                       BackgroundColor="Transparent"
                                       Focused="OnDisplayNameFocused"
                                       Unfocused="OnDisplayNameUnfocused" />
                            </Grid>
                        </Border>
                    </VerticalStackLayout>

                    <!-- Username Field -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="14"
                            Text="Username"
                            TextColor="#4E4E4E" />
                        <Border
                            Background="#EEF3F4"
                            HeightRequest="56"
                            StrokeThickness="1"
                            Stroke="{Binding UsernameFieldFocused, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#004f98|#E5E7EB'}">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="12" />
                            </Border.StrokeShape>
                            <Grid Margin="16,0" ColumnDefinitions="Auto,*">
                                <Path Grid.Column="0" 
                                      Fill="#6B7280" 
                                      VerticalOptions="Center"
                                      Margin="0,0,12,0"
                                      Data="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                <Entry Grid.Column="1"
                                       FontSize="16"
                                       Placeholder="Choose a username"
                                       PlaceholderColor="#9CA3AF"
                                       Text="{Binding SelectedItem.NickName}"
                                       BackgroundColor="Transparent"
                                       Focused="OnUsernameFocused"
                                       Unfocused="OnUsernameUnfocused" />
                            </Grid>
                        </Border>
                    </VerticalStackLayout>

                    <!-- Password Field -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="14"
                            Text="Password"
                            TextColor="#4E4E4E" />
                        <Border
                            Background="#EEF3F4"
                            HeightRequest="56"
                            StrokeThickness="1"
                            Stroke="{Binding PasswordFieldFocused, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#004f98|#E5E7EB'}">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="12" />
                            </Border.StrokeShape>
                            <Grid Margin="16,0" ColumnDefinitions="Auto,*,Auto">
                                <Path Grid.Column="0" 
                                      Fill="#6B7280" 
                                      VerticalOptions="Center"
                                      Margin="0,0,12,0"
                                      Data="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                <Entry Grid.Column="1"
                                       FontSize="16"
                                       Placeholder="Create a password"
                                       PlaceholderColor="#9CA3AF"
                                       Text="{Binding SelectedItem.PassKey}"
                                       IsPassword="{Binding SelectedItem.IsPasswordHidden}"
                                       BackgroundColor="Transparent"
                                       Focused="OnPasswordFocused"
                                       Unfocused="OnPasswordUnfocused" />
                                <Button Grid.Column="2"
                                        BackgroundColor="Transparent"
                                        Command="{Binding TogglePasswordVisibilityCommand}"
                                        Padding="8"
                                        WidthRequest="40"
                                        HeightRequest="40">
                                    <Button.ImageSource>
                                        <FontImageSource 
                                            FontFamily="FluentSystemIcons"
                                            Glyph="{Binding SelectedItem.IsPasswordHidden, Converter={StaticResource BoolToStringConverter}, ConverterParameter='&#xF208;|&#xF209;'}"
                                            Color="#6B7280"
                                            Size="20" />
                                    </Button.ImageSource>
                                </Button>
                            </Grid>
                        </Border>
                    </VerticalStackLayout>

                    <!-- Create Account Button -->
                    <Button
                        BackgroundColor="#004f98"
                        Command="{Binding SaveCommand}"
                        FontFamily="Poppins"
                        FontSize="18"
                        FontAttributes="Bold"
                        Text="{Binding IsWorking, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Creating account...|Create Account'}"
                        TextColor="White"
                        HeightRequest="56"
                        IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}">
                        <!--<Button.StrokeShape>
                            <RoundRectangle CornerRadius="12" />
                        </Button.StrokeShape>-->
                    </Button>

                    <!-- Loading Indicator -->
                    <ActivityIndicator IsVisible="{Binding IsWorking}" IsRunning="{Binding IsWorking}" Color="#004f98" />

                    <!-- Sign In Link -->
                    <HorizontalStackLayout HorizontalOptions="Center" Spacing="5" Margin="0,20,0,0">
                        <Label
                            FontFamily="Poppins"
                            FontSize="14"
                            Text="Already have an account?"
                            TextColor="#6B7280" />
                        <Label
                            FontAttributes="Bold"
                            FontFamily="Poppins"
                            FontSize="14"
                            Text="Sign in here"
                            TextColor="#004f98">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding GoToSigninCommand}" />
                            </Label.GestureRecognizers>
                        </Label>
                    </HorizontalStackLayout>
                </VerticalStackLayout>
            </VerticalStackLayout>
        </ScrollView>
    </Border>
</local:SignupFormViewBase>
