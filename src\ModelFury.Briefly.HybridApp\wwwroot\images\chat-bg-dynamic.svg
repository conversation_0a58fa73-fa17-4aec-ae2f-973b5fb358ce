<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Nothing Phone Color Gradients -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1"/>
      <stop offset="50%" style="stop-color:#fafafa;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#f5f5f5;stop-opacity:1"/>
    </linearGradient>
    
    <linearGradient id="shapeGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#eeeeee;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:0.4"/>
    </linearGradient>
    
    <linearGradient id="shapeGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#bdbdbd;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#9e9e9e;stop-opacity:0.2"/>
    </linearGradient>
    
    <radialGradient id="bubbleGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#f5f5f5;stop-opacity:0.9"/>
      <stop offset="70%" style="stop-color:#eeeeee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:0.2"/>
    </radialGradient>
    
    <!-- Noise Pattern for Texture -->
    <filter id="noise">
      <feTurbulence baseFrequency="0.9" numOctaves="1" result="noise"/>
      <feColorMatrix in="noise" type="saturate" values="0"/>
      <feComponentTransfer>
        <feFuncA type="discrete" tableValues="0 0.02 0.04 0.06"/>
      </feComponentTransfer>
      <feComposite operator="over" in2="SourceGraphic"/>
    </filter>
  </defs>
  
  <!-- Base Background with Gradient -->
  <rect width="400" height="600" fill="url(#bgGradient)"/>
  
  <!-- Floating Abstract Shapes -->
  
  <!-- Large Organic Blobs -->
  <path d="M80,120 Q150,80 220,120 Q250,180 200,240 Q130,260 80,200 Q50,160 80,120Z" 
        fill="url(#shapeGradient1)" opacity="0.6"/>
  
  <path d="M280,300 Q350,260 380,330 Q360,400 300,420 Q240,390 250,330 Q260,280 280,300Z" 
        fill="url(#shapeGradient2)" opacity="0.5"/>
  
  <path d="M50,450 Q120,420 150,480 Q130,540 80,550 Q30,520 20,470 Q30,430 50,450Z" 
        fill="url(#shapeGradient1)" opacity="0.4"/>
  
  <!-- Message Bubble Inspired Shapes -->
  <ellipse cx="320" cy="150" rx="45" ry="25" fill="url(#bubbleGradient)" opacity="0.7"/>
  <ellipse cx="100" cy="350" rx="38" ry="22" fill="url(#bubbleGradient)" opacity="0.6"/>
  <ellipse cx="280" cy="480" rx="42" ry="24" fill="url(#bubbleGradient)" opacity="0.5"/>
  
  <!-- Flowing Lines - Nothing Phone Minimalism -->
  <path d="M0,180 Q100,160 200,180 Q300,200 400,180" 
        stroke="#e0e0e0" stroke-width="1.5" fill="none" opacity="0.6"/>
  
  <path d="M0,320 Q150,300 250,320 Q350,340 400,320" 
        stroke="#bdbdbd" stroke-width="1" fill="none" opacity="0.5"/>
  
  <path d="M0,460 Q120,440 280,460 Q380,480 400,460" 
        stroke="#e0e0e0" stroke-width="0.8" fill="none" opacity="0.4"/>
  
  <!-- Geometric Accent Elements -->
  <circle cx="370" cy="80" r="12" fill="#dc2626" opacity="0.15"/>
  <circle cx="60" cy="280" r="8" fill="#dc2626" opacity="0.12"/>
  <circle cx="340" cy="520" r="10" fill="#dc2626" opacity="0.1"/>
  
  <!-- Subtle Corner Elements -->
  <path d="M0,0 L30,0 L30,2 L2,2 L2,30 L0,30 Z" fill="#9e9e9e" opacity="0.4"/>
  <path d="M370,0 L400,0 L400,30 L398,30 L398,2 L370,2 Z" fill="#9e9e9e" opacity="0.4"/>
  <path d="M0,570 L2,570 L2,598 L30,598 L30,600 L0,600 Z" fill="#9e9e9e" opacity="0.4"/>
  <path d="M370,598 L398,598 L398,570 L400,570 L400,600 L370,600 Z" fill="#9e9e9e" opacity="0.4"/>
  
  <!-- Delicate Texture Overlay -->
  <rect width="400" height="600" fill="white" filter="url(#noise)" opacity="0.3"/>
  
  <!-- Floating Particles -->
  <circle cx="150" cy="100" r="1.5" fill="#bdbdbd" opacity="0.6"/>
  <circle cx="250" cy="220" r="1" fill="#9e9e9e" opacity="0.5"/>
  <circle cx="180" cy="380" r="1.2" fill="#bdbdbd" opacity="0.4"/>
  <circle cx="320" cy="420" r="0.8" fill="#9e9e9e" opacity="0.6"/>
  <circle cx="90" cy="520" r="1.3" fill="#bdbdbd" opacity="0.5"/>
</svg>
