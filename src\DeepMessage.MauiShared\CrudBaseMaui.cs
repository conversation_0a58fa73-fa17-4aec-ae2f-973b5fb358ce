﻿using DeepMessage.Framework.Core;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace DeepMessage.MauiShared
{
    public abstract class CrudBaseMaui<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService,
        TFormModel, TFormViewModel, TKey, TFormDataService> :
         ListingBaseMaui<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService>
        where TListViewModel : class
        where TListBusinessObject : class
        where TService : IListingDataService<TListBusinessObject, TFilterBusinessObject>
        where TFilterViewModel : BaseFilterViewModel, new()
        where TFilterBusinessObject : BaseFilterBusinessObject, new()
        where TFormModel : class, new()
        where TFormViewModel : class, INotifyPropertyChanged, new()
        where TFormDataService : IFormDataService<TFormModel, TKey>

    { 
        protected CrudBaseMaui(IServiceScopeFactory scopeFactory, TKey? recordId = default): base(scopeFactory)
        {
            ScopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));

            using var scope = scopeFactory.CreateScope();
            Logger = scope.ServiceProvider.GetRequiredService<ILogger<TFormDataService>>();

            if (!EqualityComparer<TKey>.Default.Equals(recordId, default))
                _recordId = recordId;

            PropertyChanged += FormBase_PropertyChanged;
        }

        private TKey? _recordId;
        protected ILogger<TFormDataService> Logger { get; }
        private TFormViewModel _selectedItem = new TFormViewModel();

        public TFormViewModel SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                NotifyPropertyChanged();
            }
        }

        private bool _isWorking;
        public bool IsWorking
        {
            get => _isWorking;
            set
            {
                if (_isWorking != value)
                {
                    _isWorking = value;
                    NotifyPropertyChanged();
                }
            }
        }

        public IServiceScopeFactory ScopeFactory { get; }

       

        private void FormBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SelectedItem) && SelectedItem != null)
            {
                OnSelectedItemCreated(SelectedItem);
            }
        }

        protected virtual void OnSelectedItemCreated(TFormViewModel model) { }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            IsWorking = true;

            Task.Run(async () =>
            {
                try
                {
                    using var scope = ScopeFactory.CreateScope();
                    await LoadSelectLists(scope).ConfigureAwait(false);

                    if (IsNewRecord(_recordId))
                    {
                        var newItem = await CreateSelectedItem().ConfigureAwait(false);
                        MainThread.BeginInvokeOnMainThread(() => SelectedItem = newItem);
                    }
                    else
                    {
                        var crudService = scope.ServiceProvider.GetRequiredService<TFormDataService>();
                        var formModel = await crudService.GetItemByIdAsync(_recordId).ConfigureAwait(false)
                            ?? throw new InvalidDataException("Selected Item is null after service.GetItemByIdAsync");

                        var viewModel = await ConvertBusinessModelToViewModel(formModel).ConfigureAwait(false)
                            ?? throw new InvalidDataException("Item not found");

                        MainThread.BeginInvokeOnMainThread(() => SelectedItem = viewModel);
                    }

                    SelectedItem.PropertyChanged += SelectedItem_PropertyChanged;
                    //IsWorking = false;
                }
                catch (Exception ex)
                {
                    LogAndDisplayError(ex);
                }
            });
        }

        private static bool IsNewRecord(TKey? id) =>
            id == null || id.ToString() is "0" or "" or "00000000-0000-0000-0000-000000000000";

        protected virtual Task<TFormViewModel?> ConvertBusinessModelToViewModel(TFormModel formModel)
        {
            return formModel == null
                ? throw new ArgumentNullException(nameof(formModel), "formModel is null while cloning")
                : Task.FromResult(formModel.Clone<TFormViewModel?>());
        }

        public virtual void SelectedItem_PropertyChanged(object? sender, PropertyChangedEventArgs e) { }

        protected virtual Task LoadSelectLists(IServiceScope scope) => Task.CompletedTask;

        protected virtual Task<TFormViewModel> CreateSelectedItem() => Task.FromResult(new TFormViewModel());

        public virtual Task BeforeSaveAsync() => Task.CompletedTask;

        public virtual Task OnAfterSaveAsync(TKey key) => Task.CompletedTask;

        protected virtual TFormModel ConvertViewModelToBusinessModel(TFormViewModel? formViewModel)
        {
            return formViewModel == null
                ? throw new ArgumentNullException(nameof(formViewModel), "ViewModel cannot be null")
                : formViewModel.Clone<TFormModel>();
        }

        private ICommand _saveCommand;
        public ICommand SaveCommand
        {
            get
            {
                if (_saveCommand == null)
                {
                    _saveCommand = new Command(async () =>
                    {
                        await HandleFormSubmit(null!, null!);
                    });
                }
                return _saveCommand;
            }
        }

        public async Task HandleFormSubmit(object sender, EventArgs e)
        {
            try
            {
                Logger.LogInformation("Form submitted");
                IsWorking = true;

                using var scope = ScopeFactory.CreateScope();
                var crudService = scope.ServiceProvider.GetRequiredService<TFormDataService>();

                if (SelectedItem is IValidateable validateable)
                {
                    validateable.Validate();
                }

                var businessModel = ConvertViewModelToBusinessModel(SelectedItem)
                    ?? throw new InvalidDataException("Business model is null before calling SaveAsync");

                await BeforeSaveAsync().ConfigureAwait(false);
                var id = await crudService.SaveAsync(businessModel).ConfigureAwait(false);
                await OnAfterSaveAsync(id).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                LogAndDisplayError(ex);
            }
            finally
            {
                IsWorking = false;
            }
        }

        private void LogAndDisplayError(Exception ex)
        {
            Logger.LogError(ex, "An error occurred");
            //Crashes.TrackError(ex);
            MainThread.BeginInvokeOnMainThread(() => DisplayAlert("Error", ex.Message, "Ok"));
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

}
