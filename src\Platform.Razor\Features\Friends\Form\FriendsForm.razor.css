/* Custom CSS for FriendsForm component */

/* Form container enhancements */
.form-container {
    max-width: 48rem;
    margin: 0 auto;
}

/* Input field enhancements */
.form-input {
    transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
}

.form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:hover {
    border-color: #9ca3af;
}

.dark .form-input:hover {
    border-color: #6b7280;
}

/* Label enhancements */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.form-label.required::after {
    content: " *";
    color: #ef4444;
}

/* Avatar preview enhancements */
.avatar-preview {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.avatar-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .avatar-preview:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Button enhancements */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .btn-secondary:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Loading button animation */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Validation message styling */
.validation-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.dark .validation-message {
    color: #fca5a5;
}

/* Form section spacing */
.form-section {
    margin-bottom: 2rem;
}

.form-section:last-child {
    margin-bottom: 0;
}

/* Grid responsive adjustments */
@media (max-width: 640px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .form-actions button {
        width: 100%;
    }
}

@media (min-width: 641px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .form-actions {
        flex-direction: row;
        justify-content: flex-end;
        gap: 0.75rem;
    }
    
    .form-actions button {
        width: auto;
        min-width: 120px;
    }
}

/* Textarea enhancements */
.form-textarea {
    resize: vertical;
    min-height: 100px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-textarea:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Error state styling */
.form-error {
    background-color: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.dark .form-error {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #fca5a5;
}

/* Success state styling */
.form-success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.dark .form-success {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
    color: #86efac;
}

/* Focus styles for accessibility */
.focus-ring:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #3b82f6;
}

/* Card shadow enhancements */
.form-card {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.2s ease;
}

.form-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .form-card {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .form-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* Responsive typography */
@media (max-width: 640px) {
    .form-title {
        font-size: 1.5rem;
        line-height: 2rem;
    }
    
    .form-subtitle {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

@media (min-width: 641px) {
    .form-title {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }
    
    .form-subtitle {
        font-size: 1rem;
        line-height: 1.5rem;
    }
}
